package observers

import (
	"fmt"

	"github.com/goravel/framework/contracts/database/orm"
	"github.com/klphp/goravel/packages/core/hashid/facades"
)

type UserObserver struct{}

func (u *UserObserver) init() {
	fmt.Println("观察者初始化。。。。。。。。。。")
}

func (u *UserObserver) Retrieved(event orm.Event) error {
	fmt.Println("retrieved event 触发...........")
	return nil
}

func (u *UserObserver) Creating(event orm.Event) error {
	fmt.Println("creating event 触发...........")

	//创建hashId
	hashId := facades.HashId()
	strId, err := hashId.CreateHashId()
	if err != nil {
		return err
	}
	event.SetAttribute("str_id", strId)
	return nil
}
func (u *UserObserver) Created(event orm.Event) error {
	fmt.Println("created event 触发...........")
	return nil
}

func (u *UserObserver) Updating(event orm.Event) error {
	fmt.Println("updating event 触发...........")
	return nil
}

func (u *UserObserver) Updated(event orm.Event) error {
	fmt.Println("updated event 触发...........")
	return nil
}

func (u *UserObserver) Saving(event orm.Event) error {
	fmt.Println("saving event 触发...........")
	return nil
}

func (u *UserObserver) Saved(event orm.Event) error {
	fmt.Println("saved event 触发...........")
	return nil
}

func (u *UserObserver) Deleting(event orm.Event) error {
	fmt.Println("deleting event 触发...........")
	return nil
}

func (u *UserObserver) Deleted(event orm.Event) error {
	fmt.Println("deleted event 触发...........")
	return nil
}

func (u *UserObserver) ForceDeleting(event orm.Event) error {
	fmt.Println("force deleting event 触发...........")
	return nil
}

func (u *UserObserver) ForceDeleted(event orm.Event) error {
	fmt.Println("force deleted event 触发...........")
	return nil
}
