package rules

import (
	"github.com/goravel/framework/contracts/validation"
	"regexp"
)

type Mobile struct {
}

// Signature The name of the rule.
func (receiver *Mobile) Signature() string {
	return "mobile"
}

// Passes Determine if the validation rule passes.
func (receiver *Mobile) Passes(data validation.Data, val any, options ...any) bool {
	m, _ := regexp.MatchString(`^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$`, val.(string))
	return m
}

// Message Get the validation error message.
func (receiver *Mobile) Message() string {
	return "mobile is invalid"
}
