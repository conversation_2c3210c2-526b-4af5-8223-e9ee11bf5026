package rules

import (
	"github.com/goravel/framework/contracts/validation"
	"regexp"
	"strings"
)

type Username struct {
}

// Signature The name of the rule.
func (receiver *Username) Signature() string {
	return "username"
}

// Passes Determine if the validation rule passes.
func (receiver *Username) Passes(data validation.Data, val any, options ...any) bool {
	str := val.(string)
	if !strings.Contains(str, "@") {
		m, _ := regexp.MatchString(`^[\d]+$`, str)
		return !m
	}
	return false
}

// Message Get the validation error message.
func (receiver *Username) Message() string {
	return "username is invalid"
}
