package commands

import (
	"github.com/goravel/framework/contracts/console"
	"github.com/goravel/framework/contracts/console/command"
)

type Test struct {
}

// Signature The name and signature of the console command.
func (receiver *Test) Signature() string {
	return "z:test"
}

// Description The console command description.
func (receiver *Test) Description() string {
	return "测试脚本"
}

// Extend The console command extend.
func (receiver *Test) Extend() command.Extend {
	return command.Extend{}
}

// Handle Execute the console command.
func (receiver *Test) Handle(ctx console.Context) error {

	return nil
}
