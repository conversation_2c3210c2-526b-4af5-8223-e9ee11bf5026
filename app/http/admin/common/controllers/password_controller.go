package controllers

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	"github.com/klphp/goravel/app/http/requests/api"
	adminFacades "github.com/klphp/goravel/packages/admin/facades"
	facades3 "github.com/klphp/goravel/packages/core/response/facades"
)

type PasswordController struct {
	//Dependent services
}

func NewPasswordController() *PasswordController {
	return &PasswordController{}
}

// UpdateAdminPassword godoc
// @Summary 更新管理员密码
// @Description 更新当前登录管理员的密码
// @Tags 管理员
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param password body api.PasswordUpdateRequest true "密码信息"
// @Success 200 {object} map[string]interface{} "密码修改成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/password/update [post]
func (r *PasswordController) UpdateAdminPassword(ctx http.Context) http.Response {
	resp := facades3.Response().Context(ctx)

	// 获取当前登录的管理员
	adminID := ctx.Value("admin_id").(uint)
	admin, err := adminFacades.Admin().GetAdminByID(adminID)
	if err != nil {
		return resp.Error(fmt.Errorf("管理员不存在"))
	}

	// 验证请求参数
	var formData api.PasswordUpdateRequest
	errs, err := ctx.Request().ValidateRequest(&formData)
	if err != nil {
		return resp.Error(err)
	}
	if errs != nil {
		return resp.Error(fmt.Errorf(`%s`, errs.One()))
	}

	// 更新密码
	err = adminFacades.Admin().UpdateAdminPassword(admin, formData.OldPassword, formData.NewPassword)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success("密码修改成功")

}
