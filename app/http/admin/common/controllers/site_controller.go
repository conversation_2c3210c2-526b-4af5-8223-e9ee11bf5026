package controllers

import (
	"github.com/goravel/framework/contracts/http"
	af "github.com/klphp/goravel/packages/admin/facades"
	"github.com/klphp/goravel/packages/admin/models"
)

type SiteController struct {
	//Dependent services
}

func NewSiteController() *SiteController {
	return &SiteController{
		//Inject services
	}
}

// Login godoc
// @Summary 管理员登录
// @Description 管理员登录接口
// @Tags 管理员
// @Accept json
// @Produce json
// @Param login body api.SiteLoginRequest true "登录信息"
// @Success 200 {object} map[string]interface{} "返回用户信息和token"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/login [post]
func (r *SiteController) Login(ctx http.Context) http.Response {
	return af.Admin().Login(ctx)
}

// Signup godoc
// @Summary 管理员注册
// @Description 管理员注册接口
// @Tags 管理员
// @Accept json
// @Produce json
// @Param signup body api.SiteSignupRequest true "注册信息"
// @Success 200 {object} map[string]interface{} "注册成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Router /admin/signup [post]
func (r *SiteController) Signup(ctx http.Context) http.Response {
	return af.Admin().Register(ctx)
}

// Info godoc
// @Summary 获取当前管理员信息
// @Description 获取当前登录管理员的详细信息
// @Tags 管理员
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 管理员令牌"
// @Success 200 {object} map[string]interface{} "管理员信息"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/info [get]
func (r SiteController) Info(ctx http.Context) http.Response {
	//从上下文获取user
	admin := ctx.Value("user").(*models.Admin)
	return ctx.Response().Json(http.StatusOK, admin.ToResource())
}
