package v1_controllers

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/support/collect"
	adminFacades "github.com/klphp/goravel/packages/admin/facades"
	"github.com/klphp/goravel/packages/admin/models"
	reqFacades "github.com/klphp/goravel/packages/core/response/facades"
)

type AdminController struct {
}

func NewAdminController() *AdminController {
	return &AdminController{}
}

// Index godoc
// @Summary 获取管理员列表
// @Description 获取所有管理员的列表
// @Tags 管理员管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 管理员令牌"
// @Param p query int false "页码，默认为 1"
// @Param pre query int false "每页数量，默认为 10"
// @Success 200 {object} map[string]interface{} "管理员列表"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/v1/admins [get]
func (r *AdminController) Index(ctx http.Context) http.Response {
	resp := reqFacades.Response().Context(ctx)

	// 获取分页参数
	page := ctx.Request().QueryInt("p", 1)
	pre := ctx.Request().QueryInt("pre", 10)
	if page < 1 || pre < 10 {
		return resp.Error(fmt.Errorf("参数错误"))
	}

	// 获取管理员列表
	admins, total, err := adminFacades.Admin().GetAdminList(page, pre)
	if err != nil {
		return resp.Error(err)
	}

	// 使用 collect.Map 方法将每个 Admin 模型转换为 AdminResource
	items := collect.Map(admins, func(item *models.Admin, _ int) models.AdminResource {
		return item.ToResource()
	})

	paginator := map[string]interface{}{
		"data":  items,
		"total": total,
		"page":  page,
		"limit": pre,
	}

	return resp.Success(paginator)
}

// Show godoc
// @Summary 获取管理员详情
// @Description 获取指定管理员的详细信息
// @Tags 管理员管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 管理员令牌"
// @Param id path int true "管理员ID"
// @Success 200 {object} map[string]interface{} "管理员详情"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "管理员不存在"
// @Router /admin/v1/admins/{id} [get]
func (r *AdminController) Show(ctx http.Context) http.Response {
	resp := reqFacades.Response().Context(ctx)

	// 获取管理员ID
	id := ctx.Request().RouteInt("id")
	if id <= 0 {
		return resp.Error(fmt.Errorf("无效的管理员ID"))
	}

	// 获取管理员
	admin, err := adminFacades.Admin().GetAdminByID(uint(id))
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(admin.ToResource())
}
