package v1_controllers

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/support/collect"
	facades2 "github.com/klphp/goravel/packages/core/response/facades"
	"github.com/klphp/goravel/packages/user/facades"
	"github.com/klphp/goravel/packages/user/models"
)

type UserController struct {
}

func NewUserController() *UserController {
	return &UserController{}
}

// Index godoc
// @Summary 获取用户列表
// @Description 获取所有用户的列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param p query int false "页码，默认为 1"
// @Param per query int false "每页数量，默认为 10"
// @Success 200 {object} map[string]interface{} "用户列表"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/v1/users [get]
func (r *UserController) Index(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取分页参数
	page := ctx.Request().QueryInt("p", 1)
	per := ctx.Request().QueryInt("per", 10)
	if page < 1 || per < 10 {
		return resp.Error(fmt.Errorf("参数错误"))
	}

	users, total, err := facades.User().GetUserList(page, per)
	if err != nil {
		return resp.Error(err)
	}

	items := collect.Map(users, func(item *models.User, _ int) models.UserResource {
		return item.ToResource()
	})

	paginator := map[string]interface{}{
		"data":  items,
		"total": total,
		"page":  1,
		"limit": 10,
	}

	return resp.Success(paginator)
}

// Show godoc
// @Summary 获取用户详情
// @Description 获取指定用户的详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param id path int true "用户ID"
// @Success 200 {object} map[string]interface{} "用户详情"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "用户不存在"
// @Router /admin/v1/users/{id} [get]
func (r *UserController) Show(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取管理员ID
	id := ctx.Request().RouteInt("id")
	if id <= 0 {
		return resp.Error(fmt.Errorf("无效的管理员ID"))
	}

	// 获取管理员
	user, err := facades.User().GetUserByID(uint(id))
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(user.ToResource())
}
