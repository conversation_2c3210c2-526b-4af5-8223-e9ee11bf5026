package v1_controllers

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	facades3 "github.com/klphp/goravel/packages/core/response/facades"

	userModel "github.com/klphp/goravel/packages/user/models"
)

type UserController struct {
}

func NewUserController() *UserController {
	return &UserController{}
}

// Index godoc
// @Summary 获取当前用户信息
// @Description 获取当前登录用户的详细信息
// @Tags 用户
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Success 200 {object} map[string]interface{} "用户信息"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /api/v1/user [get]
func (r *UserController) Index(ctx http.Context) http.Response {
	resp := facades3.Response().Context(ctx)
	user := ctx.Value(`user`).(userModel.User)

	fmt.Println(`============== user info ================`)
	fmt.Println(user)
	return resp.Success(user.ToResource())
}
