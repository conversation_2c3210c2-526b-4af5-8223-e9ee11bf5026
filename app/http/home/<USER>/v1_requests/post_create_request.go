package v1_requests

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/contracts/validation"
)

type PostCreateRequest struct {
	Title   string `json:"title"`
	Content string `json:"content"`
}

func (r *PostCreateRequest) Authorize(ctx http.Context) error {
	return nil
}

func (r *PostCreateRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"title":   "required|max_len:80",
		"content": "required",
	}
}

func (r *PostCreateRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		"title.required":   "标题不能为空",
		"content.required": "内容不能为空",
	}
}

func (r *PostCreateRequest) Attributes(ctx http.Context) map[string]string {
	return map[string]string{}
}

func (r *PostCreateRequest) PrepareForValidation(ctx http.Context, data validation.Data) error {
	return nil
}
