package v1_requests

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/contracts/validation"
)

type UserCreateRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// Authorize 表单请求授权验证
func (r *UserCreateRequest) Authorize(ctx http.Context) error {
	return nil
}

func (r *UserCreateRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"username": "required|max_len:30|username_exists",
		"password": "required",
	}
}

// Messages 自定义错误消息
func (r *UserCreateRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		"username.required": "帐号不能为空",
		"password.required": "密码不能为空",
	}
}

// Attributes 自定义验证属性
func (r *UserCreateRequest) Attributes(ctx http.Context) map[string]string {
	return map[string]string{}
}

// PrepareForValidation 验证前清理或修改数据
func (r *UserCreateRequest) PrepareForValidation(ctx http.Context, data validation.Data) error {
	return nil
}
