package controllers

import (
	"github.com/goravel/framework/contracts/http"
	facades3 "github.com/klphp/goravel/packages/core/response/facades"
)

type TestController struct {
	//Dependent services
	//ServiceTest *Test.Service
}

func NewTestController() *TestController {
	//test, _ := utils.GetService[Test.Service](Test.ServiceName)
	return &TestController{
		//ServiceTest: &test,
	}
}

// Index godoc
// @Summary 测试接口
// @Description 测试接口，返回查询参数
// @Tags 测试
// @Accept json
// @Produce json
// @Param abc query string false "测试参数" default(default abc)
// @Success 200 {object} map[string]interface{} "返回测试参数"
// @Router /api/v1/test [get]
func (r *TestController) Index(ctx http.Context) http.Response {
	//服务提供者测试
	resp := facades3.Response().Context(ctx)

	v := ctx.Request().Query(`abc`, `default abc`)
	return resp.Success(v)
}
