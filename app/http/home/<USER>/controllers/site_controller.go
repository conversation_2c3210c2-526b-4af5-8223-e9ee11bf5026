package controllers

import (
	"github.com/goravel/framework/contracts/http"
	uf "github.com/klphp/goravel/packages/user/facades"
	"github.com/klphp/goravel/packages/user/models"
)

type SiteController struct {
	//Dependent services
}

func NewSiteController() *SiteController {
	return &SiteController{
		//Inject services
	}
}

// Login godoc
// @Summary 用户登录
// @Description 用户登录接口
// @Tags 用户
// @Accept json
// @Produce json
// @Param login body api.SiteLoginRequest true "登录信息"
// @Success 200 {object} map[string]interface{} "返回用户信息和token"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /api/login [post]
func (r *SiteController) Login(ctx http.Context) http.Response {
	return uf.User().Login(ctx)
}

// Signup godoc
// @Summary 用户注册
// @Description 用户注册接口
// @Tags 用户
// @Accept json
// @Produce json
// @Param signup body api.SiteSignupRequest true "注册信息"
// @Success 200 {object} map[string]interface{} "注册成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Router /api/signup [post]
func (r *SiteController) Signup(ctx http.Context) http.Response {
	return uf.User().Register(ctx)
}

// Info godoc
// @Summary 获取当前用户信息
// @Description 获取当前登录用户的详细信息
// @Tags 用户
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Success 200 {object} map[string]interface{} "用户信息"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /api/info [get]
func (r *SiteController) Info(ctx http.Context) http.Response {
	//上下文中获取用户信息
	user := ctx.Value(`user`).(*models.User)
	return ctx.Response().Json(http.StatusOK, user.ToResource())
}
