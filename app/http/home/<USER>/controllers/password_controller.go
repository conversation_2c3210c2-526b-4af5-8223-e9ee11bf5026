package controllers

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	"github.com/klphp/goravel/app/http/requests/api"
	facades3 "github.com/klphp/goravel/packages/core/response/facades"
	"github.com/klphp/goravel/packages/user/facades"
)

type PasswordController struct {
	//Dependent services
}

func NewPasswordController() *PasswordController {
	return &PasswordController{}
}

// UpdateUserPassword godoc
// @Summary 更新用户密码
// @Description 更新当前登录用户的密码
// @Tags 用户
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param password body api.PasswordUpdateRequest true "密码信息"
// @Success 200 {object} map[string]interface{} "密码修改成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /api/password/update [post]
func (r *PasswordController) UpdateUserPassword(ctx http.Context) http.Response {
	resp := facades3.Response().Context(ctx)

	// 获取当前登录的用户
	userID := ctx.Value("user_id").(uint)
	user, err := facades.User().GetUserByID(userID)
	if err != nil {
		return resp.Error(fmt.Errorf("获取用户信息失败"))
	}

	// 验证请求参数
	var formData api.PasswordUpdateRequest
	errors, err := ctx.Request().ValidateRequest(&formData)
	if err != nil {
		return resp.Error(err)
	}
	if errors != nil {
		return resp.Error(fmt.Errorf(`%s`, errors.One()))
	}

	// 更新密码
	err = facades.User().UpdateUserPassword(user, formData.OldPassword, formData.NewPassword)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success("密码修改成功")
}
