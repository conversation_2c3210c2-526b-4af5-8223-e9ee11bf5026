package http

import (
	"github.com/goravel/framework/contracts/http"
)

type Kernel struct {
}

// The application's global HTTP middleware stack.
// These middleware are run during every request to your application.
func (kernel Kernel) Middleware() []http.Middleware {
	return []http.Middleware{
		//全局中间件注册
		//middleware.AuthApi(),
		//middleware.AuthAdmin(),
	}
}

// Boot 启动 HTTP 服务
func (kernel Kernel) Boot() {
	// 注意：我们已经手动创建了 Swagger UI 和 JSON 文件，
	// 所以不需要使用 swag 工具自动生成文档。
	// 我们已经在 routes/web.go 中添加了 Swagger UI 的路由。
}
