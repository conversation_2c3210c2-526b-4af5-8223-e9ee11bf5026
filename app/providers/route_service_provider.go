package providers

import (
	"github.com/goravel/framework/contracts/foundation"
	contractshttp "github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	"github.com/klphp/goravel/app/http"
	"github.com/klphp/goravel/routes"
)

type RouteServiceProvider struct {
}

func (receiver *RouteServiceProvider) Register(app foundation.Application) {
}

func (receiver *RouteServiceProvider) Boot(app foundation.Application) {

	//Add HTTP middleware
	facades.Route().GlobalMiddleware(http.Kernel{}.Middleware()...)

	facades.Route().Recover(func(ctx contractshttp.Context, err any) {
		ctx.Request().Abort()
	})
	receiver.configureRateLimiting()

	routes.Web()
	routes.Api()
	routes.Admin()
}

func (receiver *RouteServiceProvider) configureRateLimiting() {

}
