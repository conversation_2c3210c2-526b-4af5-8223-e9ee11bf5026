package status

type Error struct {
	Errno   int
	Message string
}

var (
	Success   = `操作成功`
	ErrorInfo = map[string]*Error{
		"field_exists": &Error{
			Message: `重复的数据`,
			Errno:   40100,
		},
		"email is invalid": &Error{
			Message: `email无效`,
			Errno:   40101,
		},
		"mobile is invalid": &Error{
			Message: `手机号码无效`,
			Errno:   40102,
		},
		"record does not exist": &Error{
			Message: `记录不存在`,
			Errno:   40103,
		},
		"record already exists": &Error{
			Message: `记录已存在`,
			Errno:   40104,
		},
	}
)

func GetError(key string) *Error {
	v, ok := ErrorInfo[key]
	if ok {
		return v
	} else {
		return nil
	}
}
