package routes

import (
	nethttp "net/http"

	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	_ "github.com/klphp/goravel/docs" // 导入 Swagger 文档
)

func Web() {

	facades.Route().Get("/", func(ctx http.Context) http.Response {
		return ctx.Response().Json(http.StatusOK, http.Json{
			"Hello": "Goravel",
		})
	})

	// 使用 StaticFS 提供 docs 目录的静态文件
	facades.Route().StaticFS("/docs", nethttp.Dir("./docs"))

	// API 文档重定向
	facades.Route().Get("/api-docs", func(ctx http.Context) http.Response {
		return ctx.Response().Redirect(http.StatusFound, "/docs/index.html")
	})

}
