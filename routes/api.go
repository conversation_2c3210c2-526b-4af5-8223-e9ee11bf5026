package routes

import (
	"github.com/goravel/framework/contracts/route"
	"github.com/goravel/framework/facades"
	"github.com/klphp/goravel/app/http/home/<USER>/controllers"
	"github.com/klphp/goravel/app/http/home/<USER>/v1_controllers"
	"github.com/klphp/goravel/packages/common/middleware"
)

func Api() {
	prefix := facades.Config().GetString("http.route_prefix.api")
	apiRoute := facades.Route().Prefix(prefix)
	apiRoute.Middleware(middleware.AutoTransaction())

	// 公共路由
	apiRoute.Group(func(g route.Router) {
		siteController := controllers.NewSiteController()
		g.Post("/login", siteController.Login)
		g.Post("/signup", siteController.Signup)
		// v1 API路由，非认证路由
		g.Prefix("/v1").Group(func(v1 route.Router) {
			testController := controllers.NewTestController()
			v1.Any("/test", testController.Index)
		})
	})

	// 需要认证的路由
	apiRouteAuth := apiRoute.Prefix("api").Middleware(middleware.AuthSanctum("user"))
	apiRouteAuth.Group(func(g route.Router) {
		//获取当前登陆的用户信息
		siteController := controllers.NewSiteController()
		g.Get("/info", siteController.Info)
		//修改密码
		passwordController := controllers.NewPasswordController()
		g.Post("/password/update", passwordController.UpdateUserPassword)
		// v1 需要认证的路由
		g.Prefix("/v1").Group(func(v1 route.Router) {
			userController := v1_controllers.NewUserController()
			v1.Get("/user", userController.Index)
		})
	})

}
