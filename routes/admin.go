package routes

import (
	"github.com/goravel/framework/contracts/route"
	"github.com/goravel/framework/facades"
	"github.com/klphp/goravel/app/http/admin/common/controllers"
	"github.com/klphp/goravel/app/http/admin/v1/v1_controllers"
	"github.com/klphp/goravel/packages/common/middleware"
)

func Admin() {
	prefix := facades.Config().GetString("http.route_prefix.admin")
	adminRoute := facades.Route().Prefix(prefix)
	adminRoute.Middleware(middleware.AutoTransaction())
	// 公共路由
	adminRoute.Group(func(route route.Router) {
		siteController := controllers.NewSiteController()
		route.Post("/login", siteController.Login)
		route.Post("/signup", siteController.Signup)
	})

	adminRouteAuth := adminRoute.Prefix("/admin").Middleware(middleware.AuthSanctum("admin"))
	// 需要认证的路由
	adminRouteAuth.Group(func(g route.Router) {
		//获取当前登陆的管理员信息
		siteController := controllers.NewSiteController()
		g.Get("/me", siteController.Info)
		//修改密码
		passwordController := controllers.NewPasswordController()
		g.Post("/password/update", passwordController.UpdateAdminPassword)
		// v1 需要认证的路由
		g.Prefix("/v1").Group(func(v1 route.Router) {
			adminController := v1_controllers.NewAdminController()
			v1.Get("/admins", adminController.Index)
			v1.Get("/admins/{id}", adminController.Show)

			userController := v1_controllers.NewUserController()
			v1.Get("/users", userController.Index)
			v1.Get("/users/{id}", userController.Show)

		})
	})

}
