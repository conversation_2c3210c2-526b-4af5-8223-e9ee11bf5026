# Goravel Admin V1 API 文档

本文档描述了 Goravel 后台管理系统的 V1 版本 API。

## 目录

- [通用说明](#通用说明)
- [认证相关](#认证相关)
  - [管理员登录](#管理员登录)
  - [管理员退出](#管理员退出)
  - [获取当前管理员信息](#获取当前管理员信息)
  - [修改密码](#修改密码)
- [管理员管理](#管理员管理)
  - [获取管理员列表](#获取管理员列表)
  - [创建管理员](#创建管理员)
  - [获取管理员详情](#获取管理员详情)
  - [更新管理员](#更新管理员)
  - [删除管理员](#删除管理员)
- [用户管理](#用户管理)
  - [获取用户列表](#获取用户列表)
  - [创建用户](#创建用户)
  - [获取用户详情](#获取用户详情)
  - [更新用户](#更新用户)
  - [删除用户](#删除用户)
- [RBAC 权限管理](#rbac-权限管理)
  - [角色管理](#角色管理)
    - [获取角色列表](#获取角色列表)
    - [创建角色](#创建角色)
    - [获取角色详情](#获取角色详情)
    - [更新角色](#更新角色)
    - [删除角色](#删除角色)
  - [权限管理](#权限管理)
    - [获取权限列表](#获取权限列表)
    - [创建权限](#创建权限)
    - [获取权限详情](#获取权限详情)
    - [更新权限](#更新权限)
    - [删除权限](#删除权限)
  - [菜单管理](#菜单管理)
    - [获取菜单列表](#获取菜单列表)
    - [创建菜单](#创建菜单)
    - [获取菜单详情](#获取菜单详情)
    - [更新菜单](#更新菜单)
    - [删除菜单](#删除菜单)
  - [角色分配](#角色分配)
    - [分配角色给用户](#分配角色给用户)
    - [从用户移除角色](#从用户移除角色)
    - [获取用户角色](#获取用户角色)
    - [获取用户权限](#获取用户权限)
    - [检查用户是否拥有角色](#检查用户是否拥有角色)
    - [检查用户是否拥有权限](#检查用户是否拥有权限)

## 通用说明

### 基础 URL

所有 API 的基础 URL 为：`/api/admin/v1`

### 认证

大多数 API 需要通过 Bearer Token 进行认证。在请求头中添加：

```
Authorization: Bearer {token}
```

### 响应格式

所有 API 响应均为 JSON 格式，基本结构如下：

```json
{
  "status": true, // 请求是否成功
  "data": {}, // 响应数据
  "message": "", // 提示信息
  "code": 200 // 状态码
}
```

### 错误处理

当请求失败时，响应格式如下：

```json
{
  "status": false,
  "data": null,
  "message": "错误信息",
  "code": 400 // 错误状态码
}
```

常见错误状态码：

- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

## 认证相关

### 管理员登录

管理员登录系统，获取访问令牌。

**请求方法**：POST

**请求路径**：`/admin/v1/login`

**请求参数**：

| 参数名   | 类型   | 位置 | 必填 | 描述       |
| -------- | ------ | ---- | ---- | ---------- |
| account  | string | body | 是   | 管理员账号 |
| password | string | body | 是   | 管理员密码 |

**请求示例**：

```json
{
  "account": "admin",
  "password": "admin123"
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400
  },
  "message": "登录成功",
  "code": 200
}
```

### 管理员退出

管理员退出系统，使当前令牌失效。

**请求方法**：POST

**请求路径**：`/admin/v1/logout`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述              |
| ------------- | ------ | ------ | ---- | ----------------- |
| Authorization | string | header | 是   | Bearer 管理员令牌 |

**响应示例**：

```json
{
  "status": true,
  "data": null,
  "message": "退出成功",
  "code": 200
}
```

### 获取当前管理员信息

获取当前登录管理员的详细信息。

**请求方法**：GET

**请求路径**：`/admin/v1/me`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述              |
| ------------- | ------ | ------ | ---- | ----------------- |
| Authorization | string | header | 是   | Bearer 管理员令牌 |

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 1,
    "username": "admin",
    "nickname": "管理员",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "success",
  "code": 200
}
```

### 修改密码

修改当前登录管理员的密码。

**请求方法**：PUT

**请求路径**：`/admin/v1/password`

**请求参数**：

| 参数名                    | 类型   | 位置   | 必填 | 描述              |
| ------------------------- | ------ | ------ | ---- | ----------------- |
| Authorization             | string | header | 是   | Bearer 管理员令牌 |
| old_password              | string | body   | 是   | 旧密码            |
| new_password              | string | body   | 是   | 新密码            |
| new_password_confirmation | string | body   | 是   | 确认新密码        |

**请求示例**：

```json
{
  "old_password": "admin123",
  "new_password": "newpassword123",
  "new_password_confirmation": "newpassword123"
}
```

**响应示例**：

```json
{
  "status": true,
  "data": null,
  "message": "密码修改成功",
  "code": 200
}
```

## 管理员管理

### 获取管理员列表

获取所有管理员的列表。

**请求方法**：GET

**请求路径**：`/admin/v1/admins`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述                |
| ------------- | ------ | ------ | ---- | ------------------- |
| Authorization | string | header | 是   | Bearer 管理员令牌   |
| p             | int    | query  | 否   | 页码，默认为 1      |
| pre           | int    | query  | 否   | 每页数量，默认为 10 |

**响应示例**：

```json
{
  "status": true,
  "data": {
    "data": [
      {
        "id": 1,
        "username": "admin",
        "nickname": "管理员",
        "email": "<EMAIL>",
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  },
  "message": "success",
  "code": 200
}
```

### 创建管理员

创建新的管理员账号。

**请求方法**：POST

**请求路径**：`/admin/v1/admins`

**请求参数**：

| 参数名        | 类型    | 位置   | 必填 | 描述                    |
| ------------- | ------- | ------ | ---- | ----------------------- |
| Authorization | string  | header | 是   | Bearer 管理员令牌       |
| username      | string  | body   | 是   | 管理员用户名            |
| password      | string  | body   | 是   | 管理员密码              |
| nickname      | string  | body   | 否   | 管理员昵称              |
| email         | string  | body   | 是   | 管理员邮箱              |
| avatar        | string  | body   | 否   | 管理员头像              |
| status        | boolean | body   | 否   | 管理员状态，默认为 true |

**请求示例**：

```json
{
  "username": "newadmin",
  "password": "password123",
  "nickname": "新管理员",
  "email": "<EMAIL>",
  "avatar": "https://example.com/avatar.jpg",
  "status": true
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 2,
    "username": "newadmin",
    "nickname": "新管理员",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "创建成功",
  "code": 200
}
```

### 获取管理员详情

获取指定管理员的详细信息。

**请求方法**：GET

**请求路径**：`/admin/v1/admins/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述              |
| ------------- | ------ | ------ | ---- | ----------------- |
| Authorization | string | header | 是   | Bearer 管理员令牌 |
| id            | int    | path   | 是   | 管理员 ID         |

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 1,
    "username": "admin",
    "nickname": "管理员",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "success",
  "code": 200
}
```

### 更新管理员

更新指定管理员的信息。

**请求方法**：PUT

**请求路径**：`/admin/v1/admins/{id}`

**请求参数**：

| 参数名        | 类型    | 位置   | 必填 | 描述              |
| ------------- | ------- | ------ | ---- | ----------------- |
| Authorization | string  | header | 是   | Bearer 管理员令牌 |
| id            | int     | path   | 是   | 管理员 ID         |
| nickname      | string  | body   | 否   | 管理员昵称        |
| email         | string  | body   | 否   | 管理员邮箱        |
| avatar        | string  | body   | 否   | 管理员头像        |
| status        | boolean | body   | 否   | 管理员状态        |

**请求示例**：

```json
{
  "nickname": "更新后的管理员",
  "email": "<EMAIL>",
  "avatar": "https://example.com/new-avatar.jpg",
  "status": true
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 1,
    "username": "admin",
    "nickname": "更新后的管理员",
    "email": "<EMAIL>",
    "avatar": "https://example.com/new-avatar.jpg",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "更新成功",
  "code": 200
}
```

### 删除管理员

删除指定的管理员账号。

**请求方法**：DELETE

**请求路径**：`/admin/v1/admins/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述              |
| ------------- | ------ | ------ | ---- | ----------------- |
| Authorization | string | header | 是   | Bearer 管理员令牌 |
| id            | int    | path   | 是   | 管理员 ID         |

**响应示例**：

```json
{
  "status": true,
  "data": null,
  "message": "删除成功",
  "code": 200
}
```

## 用户管理

### 获取用户列表

获取所有用户的列表。

**请求方法**：GET

**请求路径**：`/admin/v1/users`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述                |
| ------------- | ------ | ------ | ---- | ------------------- |
| Authorization | string | header | 是   | Bearer 用户令牌     |
| p             | int    | query  | 否   | 页码，默认为 1      |
| per           | int    | query  | 否   | 每页数量，默认为 10 |

**响应示例**：

```json
{
  "status": true,
  "data": {
    "data": [
      {
        "id": 1,
        "username": "user1",
        "nickname": "用户1",
        "email": "<EMAIL>",
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  },
  "message": "success",
  "code": 200
}
```

### 创建用户

创建新的用户账号。

**请求方法**：POST

**请求路径**：`/admin/v1/users`

**请求参数**：

| 参数名        | 类型    | 位置   | 必填 | 描述                  |
| ------------- | ------- | ------ | ---- | --------------------- |
| Authorization | string  | header | 是   | Bearer 管理员令牌     |
| account       | string  | body   | 是   | 用户账号              |
| password      | string  | body   | 是   | 用户密码              |
| email         | string  | body   | 是   | 用户邮箱              |
| nickname      | string  | body   | 否   | 用户昵称              |
| avatar        | string  | body   | 否   | 用户头像              |
| status        | boolean | body   | 否   | 用户状态，默认为 true |

**请求示例**：

```json
{
  "account": "newuser",
  "password": "password123",
  "email": "<EMAIL>",
  "nickname": "新用户",
  "avatar": "https://example.com/avatar.jpg",
  "status": true
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 2,
    "account": "newuser",
    "nickname": "新用户",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "创建成功",
  "code": 200
}
```

### 获取用户详情

获取指定用户的详细信息。

**请求方法**：GET

**请求路径**：`/admin/v1/users/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述              |
| ------------- | ------ | ------ | ---- | ----------------- |
| Authorization | string | header | 是   | Bearer 管理员令牌 |
| id            | int    | path   | 是   | 用户 ID           |

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 1,
    "account": "user1",
    "nickname": "用户1",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "success",
  "code": 200
}
```

### 更新用户

更新指定用户的信息。

**请求方法**：PUT

**请求路径**：`/admin/v1/users/{id}`

**请求参数**：

| 参数名        | 类型    | 位置   | 必填 | 描述              |
| ------------- | ------- | ------ | ---- | ----------------- |
| Authorization | string  | header | 是   | Bearer 管理员令牌 |
| id            | int     | path   | 是   | 用户 ID           |
| nickname      | string  | body   | 否   | 用户昵称          |
| email         | string  | body   | 否   | 用户邮箱          |
| avatar        | string  | body   | 否   | 用户头像          |
| status        | boolean | body   | 否   | 用户状态          |

**请求示例**：

```json
{
  "nickname": "更新后的用户",
  "email": "<EMAIL>",
  "avatar": "https://example.com/new-avatar.jpg",
  "status": true
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 1,
    "account": "user1",
    "nickname": "更新后的用户",
    "email": "<EMAIL>",
    "avatar": "https://example.com/new-avatar.jpg",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "更新成功",
  "code": 200
}
```

### 删除用户

删除指定的用户账号。

**请求方法**：DELETE

**请求路径**：`/admin/v1/users/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述              |
| ------------- | ------ | ------ | ---- | ----------------- |
| Authorization | string | header | 是   | Bearer 管理员令牌 |
| id            | int    | path   | 是   | 用户 ID           |

**响应示例**：

```json
{
  "status": true,
  "data": null,
  "message": "删除成功",
  "code": 200
}
```

## RBAC 权限管理

### 角色管理

#### 获取角色列表

获取所有角色的列表。

**请求方法**：GET

**请求路径**：`/admin/auth/roles`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |

**响应示例**：

```json
{
  "status": true,
  "data": [
    {
      "id": 1,
      "name": "超级管理员",
      "guard_name": "admin",
      "description": "系统超级管理员",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "编辑",
      "guard_name": "admin",
      "description": "内容编辑",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ],
  "message": "success",
  "code": 200
}
```

#### 创建角色

创建新的角色。

**请求方法**：POST

**请求路径**：`/admin/auth/role`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| name          | string | body   | 是   | 角色名称        |
| guard_name    | string | body   | 是   | 守卫名称        |
| description   | string | body   | 否   | 角色描述        |

**请求示例**：

```json
{
  "name": "Test Role",
  "guard_name": "admin",
  "description": "测试角色"
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 3,
    "name": "Test Role",
    "guard_name": "admin",
    "description": "测试角色",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "创建成功",
  "code": 200
}
```

#### 获取角色详情

获取指定角色的详细信息。

**请求方法**：GET

**请求路径**：`/admin/auth/role/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| id            | int    | path   | 是   | 角色 ID         |

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 1,
    "name": "超级管理员",
    "guard_name": "admin",
    "description": "系统超级管理员",
    "permissions": [
      {
        "id": 1,
        "name": "user.view",
        "guard_name": "admin",
        "description": "查看用户"
      },
      {
        "id": 2,
        "name": "user.create",
        "guard_name": "admin",
        "description": "创建用户"
      }
    ],
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "success",
  "code": 200
}
```

#### 更新角色

更新指定角色的信息。

**请求方法**：PUT

**请求路径**：`/admin/auth/role/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| id            | int    | path   | 是   | 角色 ID         |
| name          | string | body   | 是   | 角色名称        |
| guard_name    | string | body   | 是   | 守卫名称        |
| description   | string | body   | 否   | 角色描述        |
| permissions   | array  | body   | 否   | 权限 ID 列表    |

**请求示例**：

```json
{
  "name": "Updated Test Role",
  "guard_name": "admin",
  "description": "更新后的测试角色",
  "permissions": [1, 2, 3]
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 3,
    "name": "Updated Test Role",
    "guard_name": "admin",
    "description": "更新后的测试角色",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "更新成功",
  "code": 200
}
```

#### 删除角色

删除指定的角色。

**请求方法**：DELETE

**请求路径**：`/admin/auth/role/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| id            | int    | path   | 是   | 角色 ID         |

**响应示例**：

```json
{
  "status": true,
  "data": null,
  "message": "删除成功",
  "code": 200
}
```

### 权限管理

#### 获取权限列表

获取所有权限的列表。

**请求方法**：GET

**请求路径**：`/admin/auth/permissions`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |

**响应示例**：

```json
{
  "status": true,
  "data": [
    {
      "id": 1,
      "name": "user.view",
      "guard_name": "admin",
      "description": "查看用户",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "user.create",
      "guard_name": "admin",
      "description": "创建用户",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ],
  "message": "success",
  "code": 200
}
```

#### 创建权限

创建新的权限。

**请求方法**：POST

**请求路径**：`/admin/auth/permission`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| name          | string | body   | 是   | 权限名称        |
| guard_name    | string | body   | 是   | 守卫名称        |
| description   | string | body   | 否   | 权限描述        |

**请求示例**：

```json
{
  "name": "test.permission",
  "guard_name": "admin",
  "description": "测试权限"
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 3,
    "name": "test.permission",
    "guard_name": "admin",
    "description": "测试权限",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "创建成功",
  "code": 200
}
```

#### 获取权限详情

获取指定权限的详细信息。

**请求方法**：GET

**请求路径**：`/admin/auth/permission/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| id            | int    | path   | 是   | 权限 ID         |

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 1,
    "name": "user.view",
    "guard_name": "admin",
    "description": "查看用户",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "success",
  "code": 200
}
```

#### 更新权限

更新指定权限的信息。

**请求方法**：PUT

**请求路径**：`/admin/auth/permission/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| id            | int    | path   | 是   | 权限 ID         |
| name          | string | body   | 是   | 权限名称        |
| guard_name    | string | body   | 是   | 守卫名称        |
| description   | string | body   | 否   | 权限描述        |

**请求示例**：

```json
{
  "name": "updated.test.permission",
  "guard_name": "admin",
  "description": "更新后的测试权限"
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 3,
    "name": "updated.test.permission",
    "guard_name": "admin",
    "description": "更新后的测试权限",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "更新成功",
  "code": 200
}
```

#### 删除权限

删除指定的权限。

**请求方法**：DELETE

**请求路径**：`/admin/auth/permission/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| id            | int    | path   | 是   | 权限 ID         |

**响应示例**：

```json
{
  "status": true,
  "data": null,
  "message": "删除成功",
  "code": 200
}
```

### 菜单管理

#### 获取菜单列表

获取所有菜单的列表。

**请求方法**：GET

**请求路径**：`/admin/auth/menus`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |

**响应示例**：

```json
{
  "status": true,
  "data": [
    {
      "id": 1,
      "parent_id": 0,
      "name": "Dashboard",
      "path": "/dashboard",
      "component": "Dashboard",
      "redirect": "",
      "icon": "BarChartOutline",
      "title": "仪表盘",
      "hidden": 0,
      "sort": 0,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "children": []
    },
    {
      "id": 2,
      "parent_id": 0,
      "name": "System",
      "path": "/system",
      "component": "Layout",
      "redirect": "/system/user",
      "icon": "SettingsOutline",
      "title": "系统管理",
      "hidden": 0,
      "sort": 1,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "children": [
        {
          "id": 3,
          "parent_id": 2,
          "name": "User",
          "path": "/system/user",
          "component": "system/user/index",
          "redirect": "",
          "icon": "PersonOutline",
          "title": "用户管理",
          "hidden": 0,
          "sort": 0,
          "created_at": "2023-01-01T00:00:00Z",
          "updated_at": "2023-01-01T00:00:00Z",
          "children": []
        }
      ]
    }
  ],
  "message": "success",
  "code": 200
}
```

#### 创建菜单

创建新的菜单。

**请求方法**：POST

**请求路径**：`/admin/auth/menu`

**请求参数**：

| 参数名        | 类型    | 位置   | 必填 | 描述            |
| ------------- | ------- | ------ | ---- | --------------- |
| Authorization | string  | header | 是   | Bearer 用户令牌 |
| name          | string  | body   | 是   | 菜单名称        |
| parent_id     | integer | body   | 否   | 父菜单 ID       |
| path          | string  | body   | 是   | 菜单路径        |
| component     | string  | body   | 是   | 组件路径        |
| redirect      | string  | body   | 否   | 重定向路径      |
| icon          | string  | body   | 否   | 图标名称        |
| title         | string  | body   | 是   | 菜单标题        |
| hidden        | integer | body   | 否   | 是否隐藏        |
| sort          | integer | body   | 否   | 排序            |

**请求示例**：

```json
{
  "name": "Test Menu",
  "parent_id": 0,
  "path": "/test",
  "component": "TestComponent",
  "redirect": "",
  "icon": "TestIcon",
  "title": "测试菜单",
  "hidden": 0,
  "sort": 0
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 4,
    "parent_id": 0,
    "name": "Test Menu",
    "path": "/test",
    "component": "TestComponent",
    "redirect": "",
    "icon": "TestIcon",
    "title": "测试菜单",
    "hidden": 0,
    "sort": 0,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "创建成功",
  "code": 200
}
```

#### 获取菜单详情

获取指定菜单的详细信息。

**请求方法**：GET

**请求路径**：`/admin/auth/menu/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| id            | int    | path   | 是   | 菜单 ID         |

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 1,
    "parent_id": 0,
    "name": "Dashboard",
    "path": "/dashboard",
    "component": "Dashboard",
    "redirect": "",
    "icon": "BarChartOutline",
    "title": "仪表盘",
    "hidden": 0,
    "sort": 0,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "success",
  "code": 200
}
```

#### 更新菜单

更新指定菜单的信息。

**请求方法**：PUT

**请求路径**：`/admin/auth/menu/{id}`

**请求参数**：

| 参数名        | 类型    | 位置   | 必填 | 描述            |
| ------------- | ------- | ------ | ---- | --------------- |
| Authorization | string  | header | 是   | Bearer 用户令牌 |
| id            | int     | path   | 是   | 菜单 ID         |
| name          | string  | body   | 是   | 菜单名称        |
| parent_id     | integer | body   | 否   | 父菜单 ID       |
| path          | string  | body   | 是   | 菜单路径        |
| component     | string  | body   | 是   | 组件路径        |
| redirect      | string  | body   | 否   | 重定向路径      |
| icon          | string  | body   | 否   | 图标名称        |
| title         | string  | body   | 是   | 菜单标题        |
| hidden        | integer | body   | 否   | 是否隐藏        |
| sort          | integer | body   | 否   | 排序            |

**请求示例**：

```json
{
  "name": "Updated Test Menu",
  "parent_id": 0,
  "path": "/updated-test",
  "component": "UpdatedTestComponent",
  "redirect": "",
  "icon": "UpdatedTestIcon",
  "title": "更新后的测试菜单",
  "hidden": 0,
  "sort": 0
}
```

**响应示例**：

```json
{
  "status": true,
  "data": {
    "id": 4,
    "parent_id": 0,
    "name": "Updated Test Menu",
    "path": "/updated-test",
    "component": "UpdatedTestComponent",
    "redirect": "",
    "icon": "UpdatedTestIcon",
    "title": "更新后的测试菜单",
    "hidden": 0,
    "sort": 0,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "message": "更新成功",
  "code": 200
}
```

#### 删除菜单

删除指定的菜单。

**请求方法**：DELETE

**请求路径**：`/admin/auth/menu/{id}`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| id            | int    | path   | 是   | 菜单 ID         |

**响应示例**：

```json
{
  "status": true,
  "data": null,
  "message": "删除成功",
  "code": 200
}
```

#### 获取用户菜单

获取当前用户有权限访问的菜单。

**请求方法**：GET

**请求路径**：`/admin/auth/user-menus`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |

**响应示例**：

```json
{
  "status": true,
  "data": [
    {
      "id": 1,
      "parent_id": 0,
      "name": "Dashboard",
      "path": "/dashboard",
      "component": "Dashboard",
      "redirect": "",
      "icon": "BarChartOutline",
      "title": "仪表盘",
      "hidden": 0,
      "sort": 0,
      "children": []
    },
    {
      "id": 2,
      "parent_id": 0,
      "name": "System",
      "path": "/system",
      "component": "Layout",
      "redirect": "/system/user",
      "icon": "SettingsOutline",
      "title": "系统管理",
      "hidden": 0,
      "sort": 1,
      "children": [
        {
          "id": 3,
          "parent_id": 2,
          "name": "User",
          "path": "/system/user",
          "component": "system/user/index",
          "redirect": "",
          "icon": "PersonOutline",
          "title": "用户管理",
          "hidden": 0,
          "sort": 0,
          "children": []
        }
      ]
    }
  ],
  "message": "success",
  "code": 200
}
```

### 角色分配

#### 分配角色给用户

将角色分配给指定用户。

**请求方法**：POST

**请求路径**：`/admin/auth/assign-role`

**请求参数**：

| 参数名        | 类型    | 位置   | 必填 | 描述            |
| ------------- | ------- | ------ | ---- | --------------- |
| Authorization | string  | header | 是   | Bearer 用户令牌 |
| role_id       | integer | body   | 是   | 角色 ID         |
| model_id      | integer | body   | 是   | 用户 ID         |
| model_type    | string  | body   | 是   | 用户类型        |

**请求示例**：

```json
{
  "role_id": 1,
  "model_id": 2,
  "model_type": "App\\Models\\User"
}
```

**响应示例**：

```json
{
  "status": true,
  "data": null,
  "message": "分配成功",
  "code": 200
}
```

#### 从用户移除角色

从指定用户移除角色。

**请求方法**：POST

**请求路径**：`/admin/auth/remove-role`

**请求参数**：

| 参数名        | 类型    | 位置   | 必填 | 描述            |
| ------------- | ------- | ------ | ---- | --------------- |
| Authorization | string  | header | 是   | Bearer 用户令牌 |
| role_id       | integer | body   | 是   | 角色 ID         |
| model_id      | integer | body   | 是   | 用户 ID         |
| model_type    | string  | body   | 是   | 用户类型        |

**请求示例**：

```json
{
  "role_id": 1,
  "model_id": 2,
  "model_type": "App\\Models\\User"
}
```

**响应示例**：

```json
{
  "status": true,
  "data": null,
  "message": "移除成功",
  "code": 200
}
```

#### 获取用户角色

获取当前用户的所有角色。

**请求方法**：GET

**请求路径**：`/admin/auth/user-roles`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |

**响应示例**：

```json
{
  "status": true,
  "data": [
    {
      "id": 1,
      "name": "超级管理员",
      "guard_name": "admin",
      "description": "系统超级管理员",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ],
  "message": "success",
  "code": 200
}
```

#### 获取用户权限

获取当前用户的所有权限。

**请求方法**：GET

**请求路径**：`/admin/auth/user-permissions`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |

**响应示例**：

```json
{
  "status": true,
  "data": [
    {
      "id": 1,
      "name": "user.view",
      "guard_name": "admin",
      "description": "查看用户",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "user.create",
      "guard_name": "admin",
      "description": "创建用户",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ],
  "message": "success",
  "code": 200
}
```

#### 检查用户是否拥有角色

检查当前用户是否拥有指定角色。

**请求方法**：GET

**请求路径**：`/admin/auth/has-role`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| role          | string | query  | 是   | 角色名称        |

**响应示例**：

```json
{
  "status": true,
  "data": true,
  "message": "success",
  "code": 200
}
```

#### 检查用户是否拥有权限

检查当前用户是否拥有指定权限。

**请求方法**：GET

**请求路径**：`/admin/auth/has-permission`

**请求参数**：

| 参数名        | 类型   | 位置   | 必填 | 描述            |
| ------------- | ------ | ------ | ---- | --------------- |
| Authorization | string | header | 是   | Bearer 用户令牌 |
| permission    | string | query  | 是   | 权限名称        |

**响应示例**：

```json
{
  "status": true,
  "data": true,
  "message": "success",
  "code": 200
}
```

## 注意事项

1. 所有需要认证的 API 都需要在请求头中添加 `Authorization: Bearer {token}`。
2. 分页参数 `p` 和 `per`/`pre` 必须大于等于 1 和 10，否则会返回参数错误。
3. 当请求特定资源（如管理员或用户）时，如果资源不存在，会返回 404 错误。
