package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250506073937CreateAuthRoleHasPermissionsTable struct {
}

// Signature The unique signature for the migration.
func (r *M20250506073937CreateAuthRoleHasPermissionsTable) Signature() string {
	return "20250506073937_create_auth_role_has_permissions_table"
}

// Up Run the migrations.
func (r *M20250506073937CreateAuthRoleHasPermissionsTable) Up() error {
	// permission_id: 外键，关联 permissions 表的 id。
	// role_id: 外键，关联 roles 表的 id。
	// primary(['permission_id', 'role_id']): 定义 permission_id 和 role_id 的联合主键，确保角色和权限的组合是唯一的。onDelete('cascade') 表示当关联的权限或角色被删除时，这条记录也会被删除。
	if !facades.Schema().HasTable("auth_role_has_permissions") {
		return facades.Schema().Create("auth_role_has_permissions", func(table schema.Blueprint) {
			table.UnsignedBigInteger("permission_id")
			table.Foreign("permission_id").References("id").On("auth_permissions").CascadeOnDelete()
			table.UnsignedBigInteger("role_id")
			table.Foreign("role_id").References("id").On("auth_roles").CascadeOnDelete()
			table.Primary("permission_id", "role_id")
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250506073937CreateAuthRoleHasPermissionsTable) Down() error {
	return facades.Schema().DropIfExists("auth_role_has_permissions")
}
