package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250116074655CreateUsersAddress struct {
}

// Signature The unique signature for the migration.
func (r *M20250116074655CreateUsersAddress) Signature() string {
	return "20250116074655_create_users_address"
}

// Up Run the migrations.
func (r *M20250116074655CreateUsersAddress) Up() error {
	if !facades.Schema().HasTable("users_address") {
		return facades.Schema().Create("users_address", func(table schema.Blueprint) {
			table.ID()
			table.UnsignedBigInteger(`user_id`)
			table.Foreign("user_id").References("id").On("users").CascadeOnDelete()
			table.UnsignedSmallInteger(`province`).Comment(`省`)
			table.UnsignedSmallInteger(`city`).Comment(`市`)
			table.String(`area`, 255).Comment(`区`)
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250116074655CreateUsersAddress) Down() error {
	return facades.Schema().DropIfExists("users_address")
}
