package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250116074629CreateUserTeams struct {
}

// Signature The unique signature for the migration.
func (r *M20250116074629CreateUserTeams) Signature() string {
	return "20250116074629_create_user_teams"
}

// Up Run the migrations.
func (r *M20250116074629CreateUserTeams) Up() error {
	if !facades.Schema().HasTable("user_teams") {
		return facades.Schema().Create("user_teams", func(table schema.Blueprint) {
			table.ID()
			table.String(`name`, 50)
			table.TinyInteger(`status`).Default(10).Comment(`状态`)
			table.Timestamp(`expires_at`).Nullable()
			table.Timestamps()
			//外键
			table.UnsignedBigInteger(`user_id`)
			table.Foreign("user_id").References("id").On("users").CascadeOnDelete()
			//索引
			table.Index(`status`, `user_id`)
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250116074629CreateUserTeams) Down() error {
	return facades.Schema().DropIfExists("user_teams")
}
