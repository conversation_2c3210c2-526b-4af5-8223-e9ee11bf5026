package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250506073908CreateAuthPermissionsTable struct {
}

// Signature The unique signature for the migration.
func (r *M20250506073908CreateAuthPermissionsTable) Signature() string {
	return "20250506073908_create_auth_permissions_table"
}

// Up Run the migrations.
func (r *M20250506073908CreateAuthPermissionsTable) Up() error {
	// id: 权限 ID (主键)。
	// name: 权限名称 (唯一)。例如：'edit articles', 'delete users', 'view reports'。通常采用动词 + 名词的形式。
	// guard_name: 权限所属的认证守卫。
	// timestamps: Laravel 的时间戳字段。
	if !facades.Schema().HasTable("auth_permissions") {
		return facades.Schema().Create("auth_permissions", func(table schema.Blueprint) {
			table.ID()
			table.String("name", 255).Comment("权限名称")
			table.String("guard_name", 255).Comment("认证守卫名称 (e.g., web, api)")
			table.Timestamps()
			table.Unique("name", "guard_name")
			table.Index("name", "guard_name")
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250506073908CreateAuthPermissionsTable) Down() error {
	return facades.Schema().DropIfExists("auth_permissions")
}
