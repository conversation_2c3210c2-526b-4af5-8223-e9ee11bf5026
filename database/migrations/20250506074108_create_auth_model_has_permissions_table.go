package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250506074108CreateAuthModelHasPermissionsTable struct {
}

// Signature The unique signature for the migration.
func (r *M20250506074108CreateAuthModelHasPermissionsTable) Signature() string {
	return "20250506074108_create_auth_model_has_permissions_table"
}

// Up Run the migrations.
func (r *M20250506074108CreateAuthModelHasPermissionsTable) Up() error {
	// permission_id: 外键，关联 permissions 表的 id。
	// morphs('model'): 多态关联，允许任何模型直接拥有权限。
	// primary(['permission_id', 'model_id', 'model_type']): 定义联合主键，确保一个模型只能直接拥有一个特定的权限一次。
	if !facades.Schema().HasTable("auth_model_has_permissions") {
		return facades.Schema().Create("auth_model_has_permissions", func(table schema.Blueprint) {
			table.UnsignedBigInteger("permission_id")
			table.Foreign("permission_id").References("id").On("auth_permissions").CascadeOnDelete()
			table.UnsignedBigInteger("model_id")
			table.String("model_type", 255)
			table.Primary("permission_id", "model_id", "model_type")
			table.Index("model_id", "model_type") // For efficient querying
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250506074108CreateAuthModelHasPermissionsTable) Down() error {
	return facades.Schema().DropIfExists("auth_model_has_permissions")
}
