package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250116074758CreateUsersAuthTokens struct {
}

// Signature The unique signature for the migration.
func (r *M20250116074758CreateUsersAuthTokens) Signature() string {
	return "20250116074758_create_users_auth_tokens"
}

// Up Run the migrations.
func (r *M20250116074758CreateUsersAuthTokens) Up() error {
	if !facades.Schema().HasTable("users_auth_tokens") {
		return facades.Schema().Create("users_auth_tokens", func(table schema.Blueprint) {
			/**
			  user_id    bigint(20) unsigned not null,
			  token_type bigint(20) unsigned comment 'token类型',
			  token text comment 'token' not null,
			  session_key varchar(255) comment '区',
			  created_at datetime(3),
			  updated_at datetime(3),
			*/
			table.ID()
			table.UnsignedBigInteger(`user_id`)
			table.UnsignedBigInteger(`token_type`).Nullable().Comment(`token类型`)
			table.Text(`token`).Comment(`token`)
			table.String(`session_key`, 255).Nullable().Comment(`区`)
			table.Timestamps()
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250116074758CreateUsersAuthTokens) Down() error {
	return facades.Schema().DropIfExists("users_auth_tokens")
}
