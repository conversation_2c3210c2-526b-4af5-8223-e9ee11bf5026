package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20240915060148CreateUsersTable struct {
}

// Signature The unique signature for the migration.
func (r *M20240915060148CreateUsersTable) Signature() string {
	return "20240915060148_create_users_table"
}

// Up Run the migrations.
func (r *M20240915060148CreateUsersTable) Up() error {

	return facades.Schema().Create("users", func(table schema.Blueprint) {
		table.ID(`id`)
		table.String(`str_id`)
		table.String(`username`, 36).Nullable().Comment(`账号`)
		table.String(`phone`, 36).Nullable().Comment(`手机`)
		table.String(`email`, 255).Nullable().Comment(`email`)
		table.String(`nickname`, 36).Nullable().Comment(`昵称`)
		table.String(`avatar`, 255).Nullable().Comment(`头像`)
		table.String(`password`, 255)
		table.DateTime(`email_verified_at`, 3).Nullable()
		table.String(`remember_token`, 100).Nullable()
		table.UnsignedTinyInteger(`status`).Default(10).Comment(`状态`)
		table.Timestamps()
		table.SoftDeletes()
		table.UnsignedInteger(`praise`).Default(0).Comment(`赞`)
		table.UnsignedInteger(`team_id`).Default(0).Comment(`团队`)
		table.UnsignedTinyInteger(`role`).Default(0).Comment(`组别`)
		table.UnsignedTinyInteger(`type`).Default(0).Comment(`会员类型`)
		table.UnsignedTinyInteger(`vip_level`).Default(0).Comment(`等级`)
		table.UnsignedInteger(`score`).Default(0).Comment(`积分`)
		table.Decimal(`balance`).Default(0.00).Comment(`余额`)
		table.String(`recommender`, 36).Nullable().Comment(`推荐人`)
		table.String(`id_card`, 20).Nullable().Comment(`身份证号`)
		table.UnsignedTinyInteger(`id_verify`).Default(0).Comment(`身份是否验证`)
		table.UnsignedTinyInteger(`sex`).Default(0).Comment(`性别`)
		table.Timestamp(`birthday`).Nullable().Comment(`出生日期`)
		table.Timestamp(`last_vip_date`).Nullable().Comment(`vip到期时间`)
		table.String(`last_login_ip`, 50).Nullable().Comment(`最后登陆IP`)
		table.UnsignedBigInteger(`default_address`).Nullable().Comment(`默认地址`)
		table.Unique(`str_id`)
		table.Index(`status`, `team_id`)
	})
}

// Down Reverse the migrations.
func (r *M20240915060148CreateUsersTable) Down() error {
	return facades.Schema().DropIfExists("users")
}
