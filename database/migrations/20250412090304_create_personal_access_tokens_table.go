package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250412090304CreatePersonalAccessTokensTable struct {
}

// Signature The unique signature for the migration.
func (r *M20250412090304CreatePersonalAccessTokensTable) Signature() string {
	return "20250412090304_create_personal_access_tokens_table"
}

// Up Run the migrations.
func (r *M20250412090304CreatePersonalAccessTokensTable) Up() error {
	if !facades.Schema().HasTable("personal_access_tokens") {
		return facades.Schema().Create("personal_access_tokens", func(table schema.Blueprint) {
			table.String(`id`, 64)
			table.String(`tokenable_type`, 255)
			table.UnsignedBigInteger(`tokenable_id`).Nullable()
			table.String(`name`, 255)
			table.String(`token`, 255)
			table.Text(`abilities`).Nullable()
			table.Timestamp(`last_used_at`).Nullable()
			table.Timestamp(`expires_at`).Nullable()
			table.Timestamps()
			table.Primary(`id`)
			table.Index(`tokenable_type`, `tokenable_id`)
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250412090304CreatePersonalAccessTokensTable) Down() error {
	return facades.Schema().DropIfExists("personal_access_tokens")
}
