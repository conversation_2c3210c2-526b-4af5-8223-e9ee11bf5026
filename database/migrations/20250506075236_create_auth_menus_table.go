package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250506075236CreateAuthMenusTable struct {
}

// Signature The unique signature for the migration.
func (r *M20250506075236CreateAuthMenusTable) Signature() string {
	return "20250506075236_create_auth_menus_table"
}

// Up Run the migrations.
func (r *M20250506075236CreateAuthMenusTable) Up() error {
	if !facades.Schema().HasTable("auth_menus") {
		return facades.Schema().Create("auth_menus", func(table schema.Blueprint) {
			table.ID()
			table.String("name", 255).Comment("菜单名称 (用于显示)")
			table.String("route", 255).Nullable().Comment("菜单对应的路由或链接")
			table.UnsignedBigInteger("parent_id").Nullable().Comment("父级菜单 ID")
			table.UnsignedInteger("order").Default(0).Comment("菜单排序")
			table.String("icon", 255).Nullable().Comment("菜单图标")
			table.String("guard_name", 255).Comment("菜单所属的认证守卫 (e.g., web, api)")
			table.Timestamps()
			table.Foreign("parent_id").References("id").On("auth_menus").NullOnDelete()
			table.Index("route")
			table.Index("parent_id")
			table.Index("guard_name")
			table.Index("order")
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250506075236CreateAuthMenusTable) Down() error {
	return facades.Schema().DropIfExists("auth_menus")
}
