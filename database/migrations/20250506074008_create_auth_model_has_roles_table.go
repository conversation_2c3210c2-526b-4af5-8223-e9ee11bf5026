package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250506074008CreateAuthModelHasRolesTable struct {
}

// Signature The unique signature for the migration.
func (r *M20250506074008CreateAuthModelHasRolesTable) Signature() string {
	return "20250506074008_create_auth_model_has_roles_table"
}

// Up Run the migrations.
func (r *M20250506074008CreateAuthModelHasRolesTable) Up() error {
	// role_id: 外键，关联 roles 表的 id。
	// morphs('model'): Laravel 的多态关联，允许任何模型（例如 User, Team, Organization 等）拥有角色。它会创建 model_id (关联模型的 ID) 和 model_type (关联模型的类名) 两个字段。
	// primary(['role_id', 'model_id', 'model_type']): 定义联合主键，确保一个模型只能拥有一个特定的角色一次。
	if !facades.Schema().HasTable("auth_model_has_roles") {
		return facades.Schema().Create("auth_model_has_roles", func(table schema.Blueprint) {
			table.UnsignedBigInteger("role_id")
			table.Foreign("role_id").References("id").On("auth_roles").CascadeOnDelete()
			table.UnsignedBigInteger("model_id")
			table.String("model_type", 255)
			table.Primary("role_id", "model_id", "model_type")
			table.Index("model_id", "model_type") // For efficient querying
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250506074008CreateAuthModelHasRolesTable) Down() error {
	return facades.Schema().DropIfExists("auth_model_has_roles")
}
