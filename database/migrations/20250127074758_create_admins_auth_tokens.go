package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250127074758CreateAdminsAuthTokens struct {
}

// Signature The unique signature for the migration.
func (r *M20250127074758CreateAdminsAuthTokens) Signature() string {
	return "20250127074758_create_admins_auth_tokens"
}

// Up Run the migrations.
func (r *M20250127074758CreateAdminsAuthTokens) Up() error {
	if !facades.Schema().HasTable("admins_auth_tokens") {
		return facades.Schema().Create("admins_auth_tokens", func(table schema.Blueprint) {
			table.ID()
			table.UnsignedBigInteger(`admin_id`)
			table.UnsignedBigInteger(`token_type`).Nullable().Comment(`token类型`)
			table.Text(`token`).Comment(`token`)
			table.String(`session_key`, 255).Nullable().Comment(`区`)
			table.Timestamps()
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250127074758CreateAdminsAuthTokens) Down() error {
	return facades.Schema().DropIfExists("admins_auth_tokens")
}
