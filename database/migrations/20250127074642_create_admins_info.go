package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250127074642CreateAdminsInfo struct {
}

// Signature The unique signature for the migration.
func (r *M20250127074642CreateAdminsInfo) Signature() string {
	return "20250127074642_create_admins_info"
}

// Up Run the migrations.
func (r *M20250127074642CreateAdminsInfo) Up() error {
	if !facades.Schema().HasTable("admins_info") {
		return facades.Schema().Create("admins_info", func(table schema.Blueprint) {
			table.ID()
			table.UnsignedBigInteger(`admin_id`)
			table.Foreign("admin_id").References("id").On("admins").CascadeOnDelete()
			table.String(`real_name`, 36).Nullable().Comment(`真实姓名`)
			table.String(`position`, 50).Nullable().Comment(`职位`)
			table.String(`department`, 50).Nullable().Comment(`部门`)
			table.String(`employee_id`, 36).Nullable().Comment(`员工编号`)
			table.Timestamp(`hire_date`).Nullable().Comment(`入职日期`)
			table.Text(`permissions`).Nullable().Comment(`权限JSON`)
			table.Text(`settings`).Nullable().Comment(`设置JSON`)
			table.Timestamps()
			table.Index(`admin_id`)
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250127074642CreateAdminsInfo) Down() error {
	return facades.Schema().DropIfExists("admins_info")
}
