package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250412090004CreateAdminTokenType struct {
}

// Signature The unique signature for the migration.
func (r *M20250412090004CreateAdminTokenType) Signature() string {
	return "20250412090004_create_admin_token_type"
}

// Up Run the migrations.
func (r *M20250412090004CreateAdminTokenType) Up() error {
	if !facades.Schema().HasTable("admin_token_type") {
		return facades.Schema().Create("admin_token_type", func(table schema.Blueprint) {
			table.ID()
			table.String(`name`, 100)
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250412090004CreateAdminTokenType) Down() error {
	return facades.Schema().DropIfExists("admin_token_type")
}
