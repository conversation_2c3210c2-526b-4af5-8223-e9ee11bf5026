package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250116074642CreateUsersInfo struct {
}

// Signature The unique signature for the migration.
func (r *M20250116074642CreateUsersInfo) Signature() string {
	return "20250116074642_create_users_info"
}

// Up Run the migrations.
func (r *M20250116074642CreateUsersInfo) Up() error {
	if !facades.Schema().HasTable("users_info") {
		return facades.Schema().Create("users_info", func(table schema.Blueprint) {
			table.ID()
			table.UnsignedBigInteger(`user_id`)
			table.Foreign("user_id").References("id").On("users").CascadeOnDelete()
			table.String(`qq`, 50).Nullable().Comment(`QQ`)
			table.String(`wechat`, 50).Nullable().Comment(`微信`)
			table.String(`taobao`, 255).Nullable().Comment(`淘宝`)
			table.String(`douyin`, 255).Nullable().Comment(`抖音`)
			table.String(`signature`, 255).Nullable().Comment(`签名`)
			table.UnsignedSmallInteger(`province`).Nullable().Comment(`省`)
			table.UnsignedSmallInteger(`city`).Nullable().Comment(`市`)
			table.UnsignedSmallInteger(`area`).Nullable().Comment(`区`)
			table.String(`school`, 255).Nullable().Comment(`学校`)
			table.String(`company`, 255).Nullable().Comment(`公司`)
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250116074642CreateUsersInfo) Down() error {
	return facades.Schema().DropIfExists("users_info")
}
