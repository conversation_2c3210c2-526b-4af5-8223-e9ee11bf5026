package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250116074711CreateUsersTokenType struct {
}

// Signature The unique signature for the migration.
func (r *M20250116074711CreateUsersTokenType) Signature() string {
	return "20250116074711_create_users_token_type"
}

// Up Run the migrations.
func (r *M20250116074711CreateUsersTokenType) Up() error {
	if !facades.Schema().HasTable("users_token_type") {
		return facades.Schema().Create("users_token_type", func(table schema.Blueprint) {
			/**
			  id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
			  name varchar(100) not null,
			*/
			table.ID()
			table.String(`name`, 100)
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250116074711CreateUsersTokenType) Down() error {
	return facades.Schema().DropIfExists("users_token_type")
}
