package migrations

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/facades"
)

type M20250506073840CreateAuthRolesTable struct {
}

// Signature The unique signature for the migration.
func (r *M20250506073840CreateAuthRolesTable) Signature() string {
	return "20250506073840_create_auth_roles_table"
}

// Up Run the migrations.
func (r *M20250506073840CreateAuthRolesTable) Up() error {
	// id: 角色 ID (主键)。
	// name: 角色名称 (唯一)。例如：'administrator', 'editor', 'viewer'。
	// guard_name: 角色所属的认证守卫。在一个应用中可能存在多个认证方式 (例如前端用户和 API 用户)，权限和角色可能需要区分。
	// timestamps: Laravel 的时间戳字段 (created_at, updated_at).
	if !facades.Schema().HasTable("auth_roles") {
		return facades.Schema().Create("auth_roles", func(table schema.Blueprint) {
			table.ID()
			table.String("name", 255).Comment("角色名称")
			table.String("guard_name", 255).Comment("认证守卫名称")
			table.Timestamps()
			table.Unique("name", "guard_name")
			table.Index("name", "guard_name")
		})
	}

	return nil
}

// Down Reverse the migrations.
func (r *M20250506073840CreateAuthRolesTable) Down() error {
	return facades.Schema().DropIfExists("auth_roles")
}
