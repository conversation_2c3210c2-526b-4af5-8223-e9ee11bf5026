package database

import (
	"github.com/goravel/framework/contracts/database/schema"
	"github.com/goravel/framework/contracts/database/seeder"
	"github.com/klphp/goravel/database/migrations"

	"github.com/klphp/goravel/database/seeders"
)

type Kernel struct {
}

func (kernel Kernel) Migrations() []schema.Migration {
	return []schema.Migration{
		&migrations.M20240915060148CreateUsersTable{},
		&migrations.M20250116074629CreateUserTeams{},
		&migrations.M20250116074642CreateUsersInfo{},
		&migrations.M20250116074655CreateUsersAddress{},
		&migrations.M20250116074711CreateUsersTokenType{},
		&migrations.M20250116074758CreateUsersAuthTokens{},
		&migrations.M20250127060148CreateAdminsTable{},
		&migrations.M20250127074642CreateAdminsInfo{},
		&migrations.M20250127074758CreateAdminsAuthTokens{},
		&migrations.M20250412090004CreateAdminTokenType{},
		&migrations.M20250412090304CreatePersonalAccessTokensTable{},
		&migrations.M20250506073840CreateAuthRolesTable{},
		&migrations.M20250506073908CreateAuthPermissionsTable{},
		&migrations.M20250506073937CreateAuthRoleHasPermissionsTable{},
		&migrations.M20250506074008CreateAuthModelHasRolesTable{},
		&migrations.M20250506074108CreateAuthModelHasPermissionsTable{},
		&migrations.M20250506075236CreateAuthMenusTable{},
	}
}

func (kernel Kernel) Seeders() []seeder.Seeder {
	return []seeder.Seeder{
		&seeders.DatabaseSeeder{},
	}
}
