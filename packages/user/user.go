package user

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	"github.com/klphp/gohelper/kl_array"
	"github.com/klphp/goravel/app/utils"
	"github.com/klphp/goravel/packages/common"
	facades1 "github.com/klphp/goravel/packages/core/hashid/facades"
	facades3 "github.com/klphp/goravel/packages/core/response/facades"
	sf "github.com/klphp/goravel/packages/core/sanctum/facades"
	"github.com/klphp/goravel/packages/user/models"
	"github.com/klphp/goravel/packages/user/requests"
)

type User struct {
}

func NewUser() *User {
	return &User{}
}

func (u *User) Login(ctx http.Context) http.Response {
	resp := facades3.Response().Context(ctx)

	var formData requests.SiteLoginRequest
	var err error
	errors, err := ctx.Request().ValidateRequest(&formData)
	if err != nil {
		return resp.Error(err)
	}
	if errors != nil {
		return resp.Error(fmt.Errorf("%s", errors.One()))
	}

	//帐号查询
	user := &models.User{}
	err = facades.Orm().Query().Where("username = ? OR phone = ? OR email = ?", formData.Account, formData.Account, formData.Account).First(user)
	if err != nil || user.ID == 0 {
		return resp.Error(fmt.Errorf("帐号不存在或密码不匹配"))
	}

	if facades.Hash().Check(formData.Password, user.Password) {
		//生成token，默认赋予所有权限
		Token := sf.Sanctum().Context(ctx)
		accessToken, err := Token.Generate(user, utils.GUARD_USER, []string{"*"})
		if err != nil {
			return resp.Error(err)
		}
		// Token.Clear(&user)
		return resp.Success(accessToken)
	} else {
		return resp.Error(fmt.Errorf("帐号不正确或密码不匹配"))
	}
}

func (u *User) Register(ctx http.Context) http.Response {
	resp := facades3.Response().Context(ctx)

	// 获取请求参数，并在封装的请求中实现请求验证
	var formData requests.SiteSignupRequest
	var err error

	// 验证请求参数
	errors, err := ctx.Request().ValidateRequest(&formData)
	if err != nil {
		return resp.Error(err)
	}
	if errors != nil {
		return resp.Error(fmt.Errorf("%s", errors.One()))
	}

	// json绑定model
	user := &models.User{}
	user, err = kl_array.JsonBind(formData, user)
	if err != nil {
		return resp.Error(err)
	}

	// 加密
	user.Password, _ = facades.Hash().Make(user.Password)
	// StringID
	user.StrId, _ = facades1.HashId().CreateHashId()
	// Create user
	if err := resp.Query().Create(user); err != nil {
		return resp.Error(err)
	}

	Token := sf.Sanctum().Context(ctx)
	accessToken, err := Token.Generate(user, utils.GUARD_USER, []string{"*"})

	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(accessToken)
}

// GetUserByID 通过ID获取用户
func (u *User) GetUserByID(id uint) (*models.User, error) {
	user := &models.User{}
	err := facades.Orm().Query().Where("id = ?", id).First(user)
	if common.GormNotFound(err) {
		return nil, fmt.Errorf("用户不存在")
	}
	return user, nil
}

// GetUserByStrID 通过字符串ID获取用户
func (u *User) GetUserByStrID(strID string) (*models.User, error) {
	user := &models.User{}
	err := facades.Orm().Query().Where("str_id = ?", strID).First(user)
	if common.GormNotFound(err) {
		return nil, fmt.Errorf("用户不存在")
	}
	return user, nil
}

// GetUserByAccount 通过账号获取用户
func (u *User) GetUserByAccount(account string) (*models.User, error) {
	user := &models.User{}
	err := facades.Orm().Query().Where("username = ? OR phone = ? OR email = ?", account, account, account).First(user)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// UpdateUser 更新用户信息
func (u *User) UpdateUser(user *models.User) error {
	return facades.Orm().Query().Save(user)
}

// CreateUser 创建用户
func (u *User) CreateUser(user *models.User) error {
	// 加密密码
	if user.Password != "" {
		hashedPassword, err := facades.Hash().Make(user.Password)
		if err != nil {
			return err
		}
		user.Password = hashedPassword
	}

	return facades.Orm().Query().Create(user)
}

// DeleteUser 删除用户
func (u *User) DeleteUser(id uint) error {
	if _, err := facades.Orm().Query().Delete(&models.User{}, id); err != nil {
		return err
	}
	return nil
}

// VerifyPassword 验证用户密码
func (u *User) VerifyPassword(user *models.User, password string) bool {
	return facades.Hash().Check(password, user.Password)
}

// UpdateUserStatus 更新用户状态
func (u *User) UpdateUserStatus(user *models.User, status int) error {
	user.Status = int64(status)
	return facades.Orm().Query().Save(user)
}

// UpdateUserPassword 更新用户密码
func (u *User) UpdateUserPassword(user *models.User, oldPassword string, newPassword string) error {
	// 验证旧密码是否正确
	if !u.VerifyPassword(user, oldPassword) {
		return fmt.Errorf("旧密码不正确")
	}

	// 加密新密码
	hashedPassword, err := facades.Hash().Make(newPassword)
	if err != nil {
		return err
	}
	user.Password = hashedPassword
	return facades.Orm().Query().Save(user)
}

// GetUserList 获取用户列表
func (u *User) GetUserList(page int, perPage int) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	query := facades.Orm().Query()

	// 获取总数
	err := query.Model(&models.User{}).Count(&total)
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	err = query.Offset((page - 1) * perPage).Limit(perPage).Find(&users)
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// GetUserByEmail 通过邮箱获取用户
func (u *User) GetUserByEmail(email string) (*models.User, error) {
	user := &models.User{}
	err := facades.Orm().Query().Where("email = ?", email).First(user)
	if err != nil {
		return nil, err
	}
	return user, nil
}
