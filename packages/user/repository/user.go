package repository

import (
	"github.com/klphp/goravel/app/utils"
	"github.com/klphp/goravel/packages/common"
	"github.com/klphp/goravel/packages/core/response/contracts"
	"github.com/klphp/goravel/packages/user/models"
)

type UserRepository struct {
	model *models.User
	resp  contracts.Response
}

func NewUserRepository(resp contracts.Response) *UserRepository {
	return &UserRepository{
		resp: resp,
	}
}

func (u *UserRepository) GetUserByAccount(account string) *models.User {
	var user models.User
	err := u.resp.Query().Where("status=?", utils.STATUS_ACTIVE).Where("username=? OR phone=? OR email=?", account, account, account).First(&user)

	if !common.GormNotFound(err) {
		return &user
	}
	return nil
}
