package user

import (
	"github.com/goravel/framework/contracts/foundation"
)

const Binding = "user"

var App foundation.Application

type ServiceProvider struct {
}

func (receiver *ServiceProvider) Register(app foundation.Application) {
	App = app

	app.Bind(Binding, func(app foundation.Application) (any, error) {
		return NewUser(), nil
	})
}

func (receiver *ServiceProvider) Boot(app foundation.Application) {

	//route := app.MakeRoute()
	//user := NewUser()
	//route.Get("/login", user.Login)
	//route.Get("/register", user.Register)
}
