package contracts

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/klphp/goravel/packages/user/models"
)

type User interface {
	// Login 用户登录
	Login(ctx http.Context) http.Response

	// Register 用户注册
	Register(ctx http.Context) http.Response

	// GetUserByID 通过ID获取用户
	GetUserByID(id uint) (*models.User, error)

	// GetUserByStrID 通过字符串ID获取用户
	GetUserByStrID(strID string) (*models.User, error)

	// GetUserByAccount 通过账号获取用户
	GetUserByAccount(account string) (*models.User, error)

	// UpdateUser 更新用户信息
	UpdateUser(user *models.User) error

	// CreateUser 创建用户
	CreateUser(user *models.User) error

	// DeleteUser 删除用户
	DeleteUser(id uint) error

	// VerifyPassword 验证用户密码
	VerifyPassword(user *models.User, password string) bool

	// UpdateUserStatus 更新用户状态
	UpdateUserStatus(user *models.User, status int) error

	// UpdateUserPassword 更新用户密码
	UpdateUserPassword(user *models.User, oldPassword string, newPassword string) error

	// GetUserList 获取用户列表
	GetUserList(page int, perPage int) ([]*models.User, int64, error)

	// GetUserByEmail 通过邮箱获取用户
	GetUserByEmail(email string) (*models.User, error)

	// UserManager
}

// // UserManager 可以组合多个用户相关的接口。
// type UserManager interface {
// 	UserArticleManager
// 	UserCommentManager
// 	// ... 其他用户管理接口
// }

// // UserArticleManager 定义了用户与文章关联操作的接口。
// type UserArticleManager interface {
// 	GetUserArticles(userID int) ([]models.Article, error)
// 	DeleteUserArticle(userID int, articleID int) error
// }

// // UserCommentManager 定义了用户与评论关联操作的接口。
// type UserCommentManager interface {
// 	GetUserComments(userID int) ([]models.Comment, error)
// 	DeleteUserComment(userID int, commentID int) error
// }
