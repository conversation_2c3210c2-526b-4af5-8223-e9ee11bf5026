package requests

import (
	"github.com/goravel/framework/contracts/http"
)

type PasswordUpdateRequest struct {
	OldPassword          string `json:"old_password"`
	NewPassword          string `json:"new_password"`
	PasswordConfirmation string `json:"password_confirmation"`
}

func (r *PasswordUpdateRequest) Authorize(ctx http.Context) error {
	return nil
}

func (r *PasswordUpdateRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"old_password":          "required",
		"new_password":          "required|min_len:8",
		"password_confirmation": "required|eq_field:new_password",
	}
}

func (r *PasswordUpdateRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		"old_password.required":          "旧密码不能为空",
		"new_password.required":          "新密码不能为空",
		"new_password.min_len":           "新密码不能少于8位",
		"password_confirmation.required": "确认密码不能为空",
		"password_confirmation.eq_field": "新密码与确认密码不一致",
	}
}

func (r *PasswordUpdateRequest) Attributes(ctx http.Context) map[string]string {
	return map[string]string{
		"old_password":          "旧密码",
		"new_password":          "新密码",
		"password_confirmation": "确认密码",
	}
}
