package models

import (
	"github.com/goravel/framework/database/orm"
	"github.com/goravel/framework/support/carbon"
	sm "github.com/klphp/goravel/packages/core/sanctum/models"
)

type User struct {
	orm.Model
	StrId           string          `json:"str_id" gorm:"default:null"`
	Username        string          `form:"username" json:"username" gorm:"default:null"`
	Phone           string          `form:"phone" json:"phone" gorm:"default:null"`
	Email           string          `form:"email" json:"email" gorm:"default:null"`
	Nickname        string          `form:"nickname" json:"nickname" gorm:"default:null"`
	Avatar          string          `form:"avatar" json:"avatar" gorm:"default:null"`
	Password        string          `form:"password" json:"password" gorm:"default:null"`
	EmailVerifiedAt carbon.DateTime `form:"email_verified_at" json:"email_verified_at"`
	RememberToken   string          `form:"remember_token" json:"remember_token" gorm:"default:null"`
	Status          int64           `form:"status" json:"status" gorm:"default:10"`
	orm.SoftDeletes
	Praise               int64           `form:"praise" json:"praise"`
	TeamId               int64           `form:"team_id" json:"team_id"`
	Role                 int64           `form:"role" json:"role"`
	Type                 int64           `form:"type" json:"type"`
	VipLevel             int64           `form:"vip_level" json:"vip_level"`
	Score                int64           `form:"score" json:"score"`
	Balance              float64         `form:"balance" json:"balance"`
	Recommender          string          `form:"recommender" json:"recommender" gorm:"default:null"`
	IdCard               string          `form:"id_card" json:"id_card" gorm:"default:null"`
	IdVerify             int64           `form:"id_verify" json:"id_verify"`
	Sex                  int64           `form:"sex" json:"sex"`
	Birthday             carbon.DateTime `form:"birthday" json:"birthday"`
	LastVipDate          carbon.DateTime `form:"last_vip_date" json:"last_vip_date,omitempty" `
	LastLoginIp          string          `form:"last_login_ip" json:"last_login_ip,omitempty" gorm:"default:null"`
	DefaultAddress       int64           `form:"default_address" json:"default_address,omitempty"`
	Team                 UserTeams
	Address              UsersAddress
	Info                 UsersInfo
	AuthToken            UsersAuthTokens
	PersonalAccessToken  sm.PersonalAccessTokens   `gorm:"polymorphic:Tokenable"`
	PersonalAccessTokens []sm.PersonalAccessTokens `gorm:"polymorphic:Tokenable"`
}

type UserResource struct {
	ID        uint            `json:"id"`
	Username  string          `json:"username"`
	Phone     string          `json:"phone"`
	Email     string          `json:"email"`
	Nickname  string          `json:"nickname"`
	Avatar    string          `json:"avatar"`
	Status    int64           `json:"status"`
	Praise    int64           `json:"praise"`
	CreatedAt carbon.DateTime `json:"created_at"`
}

func (u *User) ToResource() UserResource {
	return UserResource{
		ID:        u.ID,
		Username:  u.Username,
		Phone:     u.Phone,
		Email:     u.Email,
		Nickname:  u.Nickname,
		Avatar:    u.Avatar,
		Status:    u.Status,
		Praise:    u.Praise,
		CreatedAt: u.CreatedAt,
	}
}
