package models

import (
	"github.com/goravel/framework/database/orm"
)

type UsersInfo struct {
	orm.Model
	UserId    int64  `json:"user_id"`
	QQ        string `json:"qq"`
	Wechat    string `json:"wechat"`
	Taobao    string `json:"taobao"`
	Douyin    string `json:"douyin"`
	Signature string `json:"signature"`
	Province  int64  `json:"province"`
	City      int64  `json:"city"`
	Area      int64  `json:"area"`
	School    string `json:"school"`
	Company   string `json:"company"`
}
