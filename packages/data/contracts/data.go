package contracts

import (
	adminContracts "github.com/klphp/goravel/packages/admin/contracts"
	commentContracts "github.com/klphp/goravel/packages/comment/contracts"
	postContracts "github.com/klphp/goravel/packages/post/contracts"
	userContracts "github.com/klphp/goravel/packages/user/contracts"
)

type Data interface {
	GetUser(ID uint) userContracts.User
	GetAdmin(ID uint) adminContracts.Admin
	commentContracts.Comment
	postContracts.Post
}
