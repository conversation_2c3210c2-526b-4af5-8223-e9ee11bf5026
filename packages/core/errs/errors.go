package errs

import (
	"errors"
)

// 定义错误类型常量
const (
	ErrNotFound     = "not_found"        // 未找到错误：请求的资源不存在
	ErrUnauthorized = "unauthorized"     // 未授权错误：用户未经授权或权限不足
	ErrBadRequest   = "bad_request"      // 错误请求：请求参数有误或格式不正确
	ErrInternal     = "internal_error"   // 内部错误：服务器内部处理错误
	ErrValidation   = "validation_error" // 验证错误：数据验证失败
	ErrTokenInvalid = "token_invalid"    // 令牌无效：提供的令牌无效或已被撤销
	ErrTokenExpired = "token_expired"    // 令牌过期：提供的令牌已过期需要重新获取
)

// Config 错误处理配置
type Config struct {
	// Debug 是否开启调试模式
	Debug bool `yaml:"debug" mapstructure:"debug"`

	// LogErrors 是否记录错误日志
	LogErrors bool `yaml:"log_errors" mapstructure:"log_errors"`

	// DefaultCode 默认错误代码
	DefaultCode int `yaml:"default_code" mapstructure:"default_code"`
}

// AppError 应用错误结构
type AppError struct {
	Type    string
	Message string
	Err     error
	Code    int
}

// Error 实现 error 接口
func (e *AppError) Error() string {
	return e.Message
}

// Unwrap 返回原始错误
func (e *AppError) Unwrap() error {
	return e.Err
}

// ErrorManager 错误处理实现
type ErrorManager struct {
	config *Config
}

// NewErrs 创建错误处理实例
func NewErrs() *ErrorManager {
	return &ErrorManager{
		config: &Config{
			Debug:       false,
			LogErrors:   true,
			DefaultCode: 400,
		},
	}
}

// ErrorNotFoundError 创建未找到错误
func (e *ErrorManager) ErrorNotFoundError(message string, err error) error {
	return &AppError{
		Type:    ErrNotFound,
		Message: message,
		Err:     err,
		Code:    404,
	}
}

// ErrorUnauthorizedError 创建未授权错误
func (e *ErrorManager) ErrorUnauthorizedError(message string, err error) error {
	return &AppError{
		Type:    ErrUnauthorized,
		Message: message,
		Err:     err,
		Code:    401,
	}
}

// ErrorBadRequestError 创建请求错误
func (e *ErrorManager) ErrorBadRequestError(message string, err error) error {
	return &AppError{
		Type:    ErrBadRequest,
		Message: message,
		Err:     err,
		Code:    400,
	}
}

// ErrorInternalError 创建内部错误
func (e *ErrorManager) ErrorInternalError(message string, err error) error {
	return &AppError{
		Type:    ErrInternal,
		Message: message,
		Err:     err,
		Code:    500,
	}
}

// ErrorValidationError 创建验证错误
func (e *ErrorManager) ErrorValidationError(message string, err error) error {
	return &AppError{
		Type:    ErrValidation,
		Message: message,
		Err:     err,
		Code:    422,
	}
}

// ErrorTokenInvalidError 创建令牌无效错误
func (e *ErrorManager) ErrorTokenInvalidError(message string, err error) error {
	return &AppError{
		Type:    ErrTokenInvalid,
		Message: message,
		Err:     err,
		Code:    401,
	}
}

// ErrorTokenExpiredError 创建令牌过期错误
func (e *ErrorManager) ErrorTokenExpiredError(message string, err error) error {
	return &AppError{
		Type:    ErrTokenExpired,
		Message: message,
		Err:     err,
		Code:    401,
	}
}

// Is 检查错误是否为特定类型
func (e *ErrorManager) Is(err error, errorType string) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Type == errorType
	}
	return false
}

// GetCode 获取错误代码
func (e *ErrorManager) GetCode(err error) int {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Code
	}
	return e.config.DefaultCode // 使用配置的默认错误码
}

// GetMessage 获取错误消息
func (e *ErrorManager) GetMessage(err error) string {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Message
	}
	return err.Error()
}
