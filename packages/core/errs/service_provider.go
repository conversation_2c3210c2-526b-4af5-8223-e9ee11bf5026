package errs

import (
	"github.com/goravel/framework/contracts/foundation"
)

const Binding = "Errs"

var App foundation.Application

type ServiceProvider struct {
}

func (receiver *ServiceProvider) Register(app foundation.Application) {
	App = app
	app.Bind(Binding, func(app foundation.Application) (any, error) {
		return NewErrs(), nil
	})
}

func (receiver *ServiceProvider) Boot(app foundation.Application) {
	// 初始化配置
	app.Publishes("github.com/klphp/goravel/packages/core/errs", map[string]string{
		"config/errs.go": app.ConfigPath("errs.go"),
	})
}
