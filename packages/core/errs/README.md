# Errors 包

这个包提供了统一的错误处理机制，用于创建和处理应用程序中的各种错误类型。

## 功能特性

- 提供多种预定义错误类型（未找到、未授权、请求错误等）
- 支持错误代码和错误消息的统一管理
- 支持错误类型检查
- 支持配置化的错误处理

## 安装

```bash
go get github.com/klphp/goravel/packages/core/errors
```

## 使用方法

### 注册服务提供者

在 `config/app.go` 文件中注册服务提供者：

```go
"providers": []foundation.ServiceProvider{
    // ...
    &errors.ServiceProvider{},
},
```

### 使用门面

```go
import "github.com/klphp/goravel/packages/core/errors/facades"

// 创建错误
err := facades.Errors().NewNotFoundError("用户未找到", nil)

// 检查错误类型
if facades.Errors().Is(err, errors.ErrNotFound) {
    // 处理未找到错误
}

// 获取错误代码
code := facades.Errors().GetCode(err)

// 获取错误消息
message := facades.Errors().GetMessage(err)
```

## 配置

在 `.env` 文件中可以配置以下选项：

```
ERRORS_DEBUG=false
ERRORS_LOG_ERRORS=true
ERRORS_DEFAULT_CODE=500
```

或者在 `config/errors.go` 中配置：

```go
return config.Map{
    "debug": false,
    "log_errors": true,
    "default_code": 500,
}
```