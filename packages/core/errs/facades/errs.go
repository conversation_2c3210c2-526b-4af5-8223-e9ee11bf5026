package facades

import (
	"log"

	errs "github.com/klphp/goravel/packages/core/errs"
	"github.com/klphp/goravel/packages/core/errs/contracts"
)

// Errs 返回错误处理实例
// 可以在应用中通过该门面访问错误处理功能
// 例如: facades.Errs().ErrorNotFoundError("记录未找到", err)
func Errs() contracts.Errs {
	instance, err := errs.App.Make(errs.Binding)
	if err != nil {
		log.Println(err)
		return nil
	}

	return instance.(contracts.Errs)
}
