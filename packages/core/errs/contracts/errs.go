package contracts

// Errs 定义错误处理接口
type Errs interface {
	// ErrorNotFoundError 创建未找到错误
	// message: 错误消息
	// err: 原始错误
	// 返回: 包装后的错误
	ErrorNotFoundError(message string, err error) error

	// ErrorUnauthorizedError 创建未授权错误
	// message: 错误消息
	// err: 原始错误
	// 返回: 包装后的错误
	ErrorUnauthorizedError(message string, err error) error

	// ErrorBadRequestError 创建请求错误
	// message: 错误消息
	// err: 原始错误
	// 返回: 包装后的错误
	ErrorBadRequestError(message string, err error) error

	// ErrorInternalError 创建内部错误
	// message: 错误消息
	// err: 原始错误
	// 返回: 包装后的错误
	ErrorInternalError(message string, err error) error

	// ErrorValidationError 创建验证错误
	// message: 错误消息
	// err: 原始错误
	// 返回: 包装后的错误
	ErrorValidationError(message string, err error) error

	// ErrorTokenInvalidError 创建令牌无效错误
	// message: 错误消息
	// err: 原始错误
	// 返回: 包装后的错误
	ErrorTokenInvalidError(message string, err error) error

	// ErrorTokenExpiredError 创建令牌过期错误
	// message: 错误消息
	// err: 原始错误
	// 返回: 包装后的错误
	ErrorTokenExpiredError(message string, err error) error

	// Is 检查错误是否为特定类型
	// err: 要检查的错误
	// errorType: 错误类型
	// 返回: 是否为指定类型
	Is(err error, errorType string) bool

	// GetCode 获取错误代码
	// err: 错误
	// 返回: 错误代码
	GetCode(err error) int

	// GetMessage 获取错误消息
	// err: 错误
	// 返回: 错误消息
	GetMessage(err error) string
}
