package facades

import (
	"log"

	sanctum "github.com/klphp/goravel/packages/core/sanctum"
	"github.com/klphp/goravel/packages/core/sanctum/contracts"
)

// Sanctum 返回Sanctum Facade
// 可以在应用中通过该门面访问Sanctum功能
// 例如: facades.Sanctum().Generate(tokenable, guard, abilities)
func Sanctum() contracts.Sanctum {
	instance, err := sanctum.App.Make(sanctum.Binding)
	if err != nil {
		log.Println(err)
		return nil
	}

	return instance.(contracts.Sanctum)
}
