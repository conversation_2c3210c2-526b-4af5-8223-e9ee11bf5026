package sanctum

import (
	"crypto"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	hc "github.com/klphp/goravel/packages/core/hashid/contracts"
	hf "github.com/klphp/goravel/packages/core/hashid/facades"
	"github.com/klphp/goravel/packages/core/sanctum/models"
	jc "github.com/klphp/goravel/packages/core/tokenjwt/contracts"
	jf "github.com/klphp/goravel/packages/core/tokenjwt/facades"
	"github.com/klphp/goravel/packages/types"
	"github.com/pkg/errors"
)

// Sanctum token管理组件
// @Description sanctum管理组件，用于api请求的令牌生成，验证，清空，刷新，删除等操作，支持abilities管理
type Sanctum struct {
	Ctx         *http.Context
	Id          string
	Token       string
	association string
	hashId      hc.HashId
	jwt         jc.Jwt
	abilities   []string
}

// NewSanctum 创建Sanctum实例，通过依赖注入接收依赖
func NewSanctum() *Sanctum {
	return &Sanctum{
		Id:          ``,
		Token:       ``,
		association: `PersonalAccessTokens`,
		hashId:      hf.HashId(),
		jwt:         jf.Jwt(),
		abilities:   []string{},
	}
}

// Context 设置上下文
func (s *Sanctum) Context(ctx http.Context) *Sanctum {
	s.Ctx = &ctx
	return s
}

// GetID 获取ID
func (s *Sanctum) GetID() string {
	return s.Id
}

// GetToken 获取Token
func (s *Sanctum) GetToken() string {
	return s.Token
}

// initToken 初始化
func (s *Sanctum) initToken() {
	s.Id = ``
	s.Token = ``
	s.abilities = []string{}
}

// Generate 生成token，支持abilities
func (s *Sanctum) Generate(tokenable types.Tokenable, guard string, abilities []string) (string, error) {

	if s.Ctx == nil {
		return ``, errors.New(`context is nil`)
	}

	// 使用注入的hashId接口
	tokenId, err := s.hashId.CreateHashId()
	if err != nil {
		return ``, errors.Wrap(err, "创建HashId失败")
	}

	// 使用注入的jwt接口
	_ = s.jwt.Context(s.Ctx)
	token, err := s.jwt.GenerateJwt(tokenable, guard, tokenId)
	if err != nil {
		return ``, errors.Wrap(err, "令牌创建失败")
	}

	h := crypto.MD5.New()
	h.Write([]byte(token))
	cryptToken := fmt.Sprintf("%x", h.Sum(nil))

	// 将abilities转换为JSON字符串
	abilitiesJSON := "*"
	if len(abilities) > 0 {
		abilitiesBytes, err := json.Marshal(abilities)
		if err != nil {
			return ``, errors.Wrap(err, "abilities序列化失败")
		}
		abilitiesJSON = string(abilitiesBytes)
	}

	accessToken := &models.PersonalAccessTokens{
		ID:            tokenId,
		TokenableType: fmt.Sprintf("%T", tokenable),
		TokenableID:   tokenable.GetID(),
		Name:          `api_token`,
		Token:         cryptToken,
		Abilities:     abilitiesJSON,
	}

	// 直接保存令牌
	err = facades.Orm().Query().Create(accessToken)

	if err != nil {
		return token, errors.Wrap(err, "Token存储失败")
	}

	return fmt.Sprintf("%s|%s", tokenId, token), nil
}

// VerifyTokenFormat 验证token格式
func (s *Sanctum) VerifyTokenFormat(token string) bool {
	splitArray := strings.Split(token, `|`)
	if strings.Contains(token, `|`) && len(splitArray) == 2 && splitArray[0] != "" && splitArray[1] != "" {
		s.Id = splitArray[0]
		s.Token = splitArray[1]
		return true
	} else {
		return false
	}
}

// findPersonalAccessToken 查询token
func (s *Sanctum) findPersonalAccessToken() *models.PersonalAccessTokens {
	if s.Id == "" || s.Token == "" {
		return nil
	}

	accessToken := models.PersonalAccessTokens{
		ID: s.Id,
	}
	err := facades.Orm().Query().First(&accessToken)
	if err != nil {
		return nil
	}

	// 解析abilities
	if accessToken.Abilities != "" && accessToken.Abilities != "*" {
		err = json.Unmarshal([]byte(accessToken.Abilities), &s.abilities)
		if err != nil {
			// 解析失败时设置为空数组
			s.abilities = []string{}
		}
	} else if accessToken.Abilities == "*" {
		// 通配符表示拥有所有权限
		s.abilities = []string{"*"}
	}

	return &accessToken
}

// Verify 验证token
func (s *Sanctum) Verify(token string) bool {
	s.initToken()
	if s.VerifyTokenFormat(token) {
		accessToken := s.findPersonalAccessToken()
		if accessToken != nil && accessToken.Token != `` {
			// 使用crypto/md5计算哈希值
			h := crypto.MD5.New()
			h.Write([]byte(s.Token))
			if fmt.Sprintf("%x", h.Sum(nil)) == accessToken.Token {
				return true
			}
		}
	}
	return false
}

// Can 检查token是否有指定能力
func (s *Sanctum) Can(ability string) bool {
	// 如果abilities包含通配符，表示拥有所有权限
	for _, a := range s.abilities {
		if a == "*" {
			return true
		}
		if a == ability {
			return true
		}
	}
	return false
}

// Cant 检查token是否没有指定能力
func (s *Sanctum) Cant(ability string) bool {
	return !s.Can(ability)
}

// Count Tokens计数
func (s *Sanctum) Count(tokenable types.Tokenable) int64 {
	q := facades.Orm().Query()
	var count int64
	err := q.Where("tokenable_type = ? AND tokenable_id = ?", fmt.Sprintf("%T", tokenable), tokenable.GetID()).Count(&count).Error
	if err != nil {
		return 0
	}
	return count
}

// Clear 清空指定用户token
func (s *Sanctum) Clear(tokenable types.Tokenable) {
	s.initToken()
	_, _ = facades.Orm().Query().Where("tokenable_type = ? AND tokenable_id = ?", fmt.Sprintf("%T", tokenable), tokenable.GetID()).Delete(&models.PersonalAccessTokens{})
}
