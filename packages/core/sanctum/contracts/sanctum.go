package contracts

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/klphp/goravel/packages/core/sanctum"
	"github.com/klphp/goravel/packages/types"
)

// Sanctum 定义Sanctum模块的契约接口
type Sanctum interface {
	// Context 设置上下文
	Context(ctx http.Context) *sanctum.Sanctum

	// Generate 生成token
	Generate(tokenable types.Tokenable, guard string, abilities []string) (string, error)

	// GetID 获取Token ID
	GetID() string

	// GetToken 获取Token字符串
	GetToken() string

	// VerifyTokenFormat 验证token格式
	VerifyTokenFormat(token string) bool

	// Verify 验证token
	Verify(token string) bool

	// Can 检查token是否有指定能力
	Can(ability string) bool

	// Cant 检查token是否没有指定能力
	Cant(ability string) bool

	// Count Tokens计数
	Count(tokenable types.Tokenable) int64

	// Clear 清空指定用户token
	Clear(tokenable types.Tokenable)
}
