package sanctum

import (
	"github.com/goravel/framework/contracts/foundation"
)

type ServiceProvider struct {
}

func (receiver *ServiceProvider) Register(app foundation.Application) {
	App = app

	app.Bind(Binding, func(app foundation.Application) (any, error) {

		// 创建Sanctum实例并注入依赖
		return NewSanctum(), nil
	})
}

// Boot 启动Sanctum服务
func (receiver *ServiceProvider) Boot(app foundation.Application) {
	// 发布配置文件
	app.Publishes("github.com/klphp/goravel/packages/core/sanctum", map[string]string{
		"config/sanctum.go": app.ConfigPath("sanctum.go"),
	})
}
