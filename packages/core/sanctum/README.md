# Sanctum 认证管理模块

## 简介

Sanctum 是一个仿 Laravel Sanctum 的认证管理模块，用于 API 请求的令牌生成、验证、清空、刷新、删除等操作，支持 abilities 权限管理。

## 特性

- 支持多态关联，可以同时管理 admin 和 user 用户的认证
- 使用 str_id|jwt 的 md5 值组合而成 access_token，用于认证
- 支持 abilities 来判断用户权限
- 提供完整的中间件支持，方便在路由中使用

## 安装配置

1. 确保已经在 `config/app.go` 中注册了 Sanctum 服务提供者：

```go
&sanctum.ServiceProvider{},
```

2. 发布配置文件（可选）：

```bash
go run . artisan vendor:publish --provider="github.com/klphp/goravel/packages/core/sanctum"
```

3. 确保已经运行了数据库迁移，创建了 `personal_access_tokens` 表：

```bash
go run . artisan migrate
```

## 使用方法

### 生成令牌

```go
// 导入sanctum包
import "github.com/klphp/goravel/packages/core/sanctum"

// 为用户生成令牌
token, err := sanctum.GenerateToken(ctx, user, "user", []string{"read", "write"})

// 或者使用门面
import "github.com/klphp/goravel/packages/core/sanctum/facades"

token, err := facades.Sanctum().Context(ctx).Generate(user, "user", []string{"read", "write"})

// 为管理员生成令牌
token, err := sanctum.GenerateToken(ctx, admin, "admin", []string{"read", "write", "admin"})
```

### 验证令牌

```go
// 验证令牌是否有效
isValid := sanctum.VerifyToken(ctx, token)

// 检查令牌是否有特定权限
hasAbility := sanctum.TokenCan(ctx, token, "admin")
```

### 在路由中使用中间件

```go
// 导入中间件
import "github.com/klphp/goravel/packages/core/sanctum/middleware"

// 使用 Sanctum 中间件保护路由
router.Middleware(middleware.AuthSanctum("user")).Get("/user/profile", controller.GetProfile)

// 使用 Sanctum 中间件并要求特定权限
router.Middleware(middleware.AuthSanctum("admin", "manage-users")).Get("/admin/users", controller.ListUsers)
```

### 清除令牌

```go
// 清除用户的所有令牌
sanctum.ClearTokens(user)

// 或者使用门面
facades.Sanctum().Clear(user)
```

### 获取当前用户

在经过 Sanctum 中间件验证的路由中，可以通过上下文获取当前用户：

```go
// 在控制器中获取当前用户
func (c *Controller) GetProfile(ctx http.Context) http.Response {
    user := ctx.Value("user")
    // 使用用户信息...
    return ctx.Response().Success().Json(http.Json{
        "user": user,
    })
}
```

## 实现 Tokenable 接口

要使用 Sanctum 认证，您的用户模型需要实现 `Tokenable` 接口：

```go
// User 模型实现 Tokenable 接口
func (u *User) GetID() uint {
    return u.ID
}

func (u *User) GetStrID() string {
    return fmt.Sprintf("%d", u.ID)
}
```

## 多态关联

Sanctum 支持多态关联，可以同时管理不同类型的用户认证：

```go
// 查询特定用户的所有令牌
tokenCount := facades.Sanctum().Count(user)

// 清除特定用户的所有令牌
facades.ClearTokens(user)
```
