package models

import (
	"github.com/goravel/framework/support/carbon"
)

// PersonalAccessTokens 个人访问令牌模型
// 支持多态关联，可以关联到user和admin
type PersonalAccessTokens struct {
	ID            string          `json:"id" gorm:"primaryKey"`
	TokenableType string          `json:"tokenable_type" gorm:"index:idx_tokenable"`
	TokenableID   uint            `json:"tokenable_id" gorm:"index:idx_tokenable"`
	Name          string          `json:"name"`
	Token         string          `json:"token" gorm:"index"`
	Abilities     string          `json:"abilities"`
	LastUsedAt    carbon.DateTime `json:"last_used_at"`
	ExpiresAt     carbon.DateTime `json:"expires_at"`
	CreatedAt     carbon.DateTime `gorm:"autoCreateTime;column:created_at" json:"created_at"`
	UpdatedAt     carbon.DateTime `gorm:"autoUpdateTime;column:updated_at" json:"updated_at"`
}

// TableName 指定表名
func (p *PersonalAccessTokens) TableName() string {
	return "personal_access_tokens"
}

// GetTokenableType 获取关联的模型类型
func (p *PersonalAccessTokens) GetTokenableType() string {
	return p.TokenableType
}

// GetTokenableID 获取关联的模型ID
func (p *PersonalAccessTokens) GetTokenableID() uint {
	return p.TokenableID
}

// SetTokenable 设置关联的模型
func (p *PersonalAccessTokens) SetTokenable(tokenableType string, tokenableID uint) {
	p.TokenableType = tokenableType
	p.TokenableID = tokenableID
}
