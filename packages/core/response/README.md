# Response 包

这个包提供了统一的 HTTP 响应处理机制，用于创建和处理应用程序中的各种响应类型。

## 功能特性

- 提供统一的响应格式
- 支持事务自动提交和回滚
- 支持分页响应
- 支持带元数据的响应
- 支持错误响应处理

## 安装

```bash
go get github.com/klphp/goravel/packages/core/response
```

## 使用方法

### 注册服务提供者

在 `config/app.go` 文件中注册服务提供者：

```go
"providers": []foundation.ServiceProvider{
    // ...
    &response.ServiceProvider{},
},
```

### 使用门面

```go
import "github.com/klphp/goravel/packages/core/response/facades"

// 在控制器中使用
func (c *UserController) Show(ctx http.Context) http.Response {
    // 设置上下文
    resp := facades.Response().Context(ctx)

    // 成功响应
    return resp.Success(map[string]interface{}{
        "user": user,
    })

    // 分页响应
    return resp.WithPagination(users, total, page, perPage)

    // 错误响应
    return resp.Error(err)
}
```

## 配置

在 `.env` 文件中可以配置以下选项：

```
RESPONSE_AUTO_TRANSACTION=true
RESPONSE_DEFAULT_SUCCESS_CODE=200
RESPONSE_DEFAULT_ERROR_CODE=400
```

或者在 `config/response.go` 中配置：

```go
return config.Map{
    "auto_transaction": true,
    "default_success_code": 200,
    "default_error_code": 400,
}
```
