package response

import (
	"log"

	"github.com/goravel/framework/contracts/database/orm"
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	"github.com/klphp/goravel/packages/core/errs"
	"github.com/klphp/goravel/packages/core/errs/contracts"
	ef "github.com/klphp/goravel/packages/core/errs/facades"
)

// Response 响应处理结构体
type Response struct {
	Ctx    *http.Context
	method string
	config *Config
	errs   contracts.Errs
}

type Config struct {
	AutoTransaction    bool
	DefaultSuccessCode int
	DefaultErrorCode   int
}

// NewResponse 创建响应处理实例
func NewResponse() *Response {
	return &Response{
		config: &Config{
			AutoTransaction:    facades.Config().GetBool("response.auto_transaction", true),
			DefaultSuccessCode: facades.Config().GetInt("response.default_success_code", 200),
			DefaultErrorCode:   facades.Config().GetInt("response.default_error_code", 400),
		},
	}
}

// Context 设置上下文
func (r *Response) Context(ctx http.Context) *Response {
	r.Ctx = &ctx
	r.method = ctx.Request().Method()
	// 初始化错误处理实例
	if r.errs == nil {
		r.errs = ef.Errs()
	}

	return r
}

// Query 获取查询构建器
func (r *Response) Query() orm.Query {
	return (*r.Ctx).Value("query").(orm.Query)
}

// commit 提交事务
func (r *Response) commit() {
	if r.method != "GET" && r.config.AutoTransaction {
		_ = r.Query().Commit()
	}
}

// rollback 回滚事务
func (r *Response) rollback() {
	if r.method != "GET" && r.config.AutoTransaction {
		log.Println(`transaction rollback.....................................`)
		_ = r.Query().Rollback()
	}
}

// Success 成功响应
func (r *Response) Success(data interface{}) http.Response {
	if r.Ctx == nil {
		panic("context is nil")
	}

	r.commit()
	c := *r.Ctx

	return c.Response().Status(r.config.DefaultSuccessCode).Json(http.Json{
		"status": true,
		"data":   data,
	})
}

// WithPagination 带分页的成功响应
func (r *Response) WithPagination(data interface{}, total int64, page int, perPage int) http.Response {
	if r.Ctx == nil {
		panic("context is nil")
	}

	r.commit()
	c := *r.Ctx

	return c.Response().Status(r.config.DefaultSuccessCode).Json(http.Json{
		"status": true,
		"data":   data,
		"meta": map[string]interface{}{
			"total":     total,
			"page":      page,
			"per_page":  perPage,
			"last_page": (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// WithMeta 带元数据的成功响应
func (r *Response) WithMeta(data interface{}, meta map[string]interface{}) http.Response {
	if r.Ctx == nil {
		panic("context is nil")
	}

	r.commit()
	c := *r.Ctx

	return c.Response().Status(r.config.DefaultSuccessCode).Json(http.Json{
		"status": true,
		"data":   data,
		"meta":   meta,
	})
}

// Error 错误响应
func (r *Response) Error(err error) http.Response {

	if r.Ctx == nil {
		panic("context is nil")
	}

	// 记录错误日志
	// facades.Log().Warning(err.Error())

	// 尝试将错误转换为应用错误
	if appErr, ok := err.(*errs.AppError); ok {
		return r.ErrorWithStatus(err, appErr.Code)
	}

	// 处理预定义错误类型
	if r.errs.Is(err, errs.ErrNotFound) || r.errs.Is(err, errs.ErrUnauthorized) ||
		r.errs.Is(err, errs.ErrBadRequest) || r.errs.Is(err, errs.ErrInternal) ||
		r.errs.Is(err, errs.ErrValidation) || r.errs.Is(err, errs.ErrTokenInvalid) ||
		r.errs.Is(err, errs.ErrTokenExpired) {
		// 获取预定义错误的状态码
		code := r.errs.GetCode(err)
		c := *r.Ctx
		if r.method != "GET" && r.config.AutoTransaction {
			r.rollback()
		}
		return c.Response().Status(code).Json(http.Json{
			"status":  false,
			"message": r.errs.GetMessage(err),
			"code":    code,
		})
	}

	// 处理一般错误，使用默认错误码
	return r.ErrorWithStatus(err, r.config.DefaultErrorCode)
}

// ErrorWithStatus 带状态码的错误响应
func (r *Response) ErrorWithStatus(err error, status int) http.Response {
	if r.Ctx == nil {
		panic("context is nil")
	}
	r.rollback()
	c := *r.Ctx

	// 删除测试代码，确保错误响应能正确返回
	return c.Response().Status(status).Json(http.Json{
		"status":  false,
		"message": r.errs.GetMessage(err),
		"code":    status,
	})
}

// ErrorWithCode 带错误码的错误响应
func (r *Response) ErrorWithCode(err error, code int) http.Response {
	if r.Ctx == nil {
		panic("context is nil")
	}

	r.rollback()
	c := *r.Ctx

	// 获取HTTP状态码
	status := r.errs.GetCode(err)

	return c.Response().Status(status).Json(http.Json{
		"status":  false,
		"message": r.errs.GetMessage(err),
		"code":    code,
	})
}

// HandleAppError 处理应用错误
func (r *Response) HandleAppError(appErr interface{}) http.Response {
	if r.Ctx == nil {
		panic("context is nil")
	}

	r.rollback()
	c := *r.Ctx

	// 将 interface{} 类型转换为 *Errs.AppError 类型以访问 Code 字段
	if err, ok := appErr.(*errs.AppError); ok {
		return c.Response().Status(err.Code).Json(http.Json{
			"status":  false,
			"message": err.Message,
			"code":    err.Code,
			"type":    err.Type,
		})
	}

	// 如果不是AppError类型，返回默认错误响应
	return c.Response().Status(r.config.DefaultErrorCode).Json(http.Json{
		"status":  false,
		"message": "未知错误",
		"code":    r.config.DefaultErrorCode,
	})
}
