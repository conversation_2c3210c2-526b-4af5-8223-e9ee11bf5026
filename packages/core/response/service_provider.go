package response

import (
	"github.com/goravel/framework/contracts/foundation"
)

const Binding = "Response"

var App foundation.Application

type ServiceProvider struct {
}

func (receiver *ServiceProvider) Register(app foundation.Application) {
	App = app

	app.Bind(Binding, func(app foundation.Application) (any, error) {
		return NewResponse(), nil
	})
}

func (receiver *ServiceProvider) Boot(app foundation.Application) {
	// 启动时的操作，如果有需要
	app.Publishes("github.com/klphp/goravel/packages/core/response", map[string]string{
		"config/response.go": app.ConfigPath("response.go"),
	})
}
