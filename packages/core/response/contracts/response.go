package contracts

import (
	"github.com/goravel/framework/contracts/database/orm"
	"github.com/goravel/framework/contracts/http"
	"github.com/klphp/goravel/packages/core/response"
)

// Response 定义响应处理接口
type Response interface {
	// Context 设置上下文
	Context(ctx http.Context) *response.Response

	// Query 获取查询构建器
	Query() orm.Query

	// Success 成功响应
	Success(data interface{}) http.Response

	// WithPagination 带分页的成功响应
	WithPagination(data interface{}, total int64, page int, perPage int) http.Response

	// WithMeta 带元数据的成功响应
	WithMeta(data interface{}, meta map[string]interface{}) http.Response

	// Error 错误响应
	Error(err error) http.Response

	// ErrorWithStatus 带状态码的错误响应
	ErrorWithStatus(err error, status int) http.Response

	// ErrorWithCode 带错误码的错误响应
	ErrorWithCode(err error, code int) http.Response

	// HandleAppError 处理应用错误
	HandleAppError(appErr interface{}) http.Response
}
