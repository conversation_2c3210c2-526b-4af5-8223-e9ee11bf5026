package hashid

import (
	"github.com/goravel/framework/contracts/foundation"
)

const Binding = "HashId"

type ServiceProvider struct {
}

var App foundation.Application

func (receiver *ServiceProvider) Register(app foundation.Application) {
	App = app
	app.Bind(Binding, func(app foundation.Application) (any, error) {
		return NewHashId(app.MakeConfig()), nil
	})
}

func (receiver *ServiceProvider) Boot(app foundation.Application) {
	app.Publishes("github.com/klphp/goravel/packages/core/hashid", map[string]string{
		"config/hash_id.go": app.ConfigPath("hash_id.go"),
	}, "hash-id-config")
}
