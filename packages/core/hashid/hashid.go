package hashid

import (
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"github.com/goravel/framework/contracts/config"
	"github.com/speps/go-hashids/v2"
)

type HashId struct {
	hd *hashids.HashIDData
}

func NewHashId(config config.Config) *HashId {

	hd := hashids.NewData()
	hd.Salt = config.GetString("APP_KEY")
	hd.Alphabet = config.GetString("HashId.alphabet")
	hd.MinLength = config.GetInt("HashId.min_length")
	return &HashId{
		hd: hd,
	}
}

// RandInt 生成指定区间随机数
func (h *HashId) RandInt(min, max int) int {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	return r.Intn(max-min) + min
}

func (h *HashId) TimeUnixNano() int {
	return int(time.Now().UnixNano() / 1e6)
}

func (h *HashId) CreateHashId() (string, error) {
	//时间戳
	timeNo := strconv.Itoa(h.TimeUnixNano())
	//1000-9999随机数
	randId := strconv.Itoa(h.RandInt(1000, 9999))
	//拼接时间戳和随机数
	strId := fmt.Sprintf("%s%s", timeNo, randId)
	//转换为int类型
	numId, _ := strconv.Atoi(strId)

	//生成hashid
	return h.Encrypt(numId)
}

func (h *HashId) Encrypt(id int) (string, error) {
	hash, _ := hashids.NewWithData(h.hd)
	e, err := hash.Encode([]int{id})
	if err != nil {
		return "", err
	}
	return e, nil
}

func (h *HashId) Decrypt(hashId string) (int, error) {
	hash, _ := hashids.NewWithData(h.hd)
	d, err := hash.DecodeWithError(hashId)
	if err != nil {
		return 0, err
	}
	return d[0], nil
}
