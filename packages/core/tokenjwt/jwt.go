package tokenjwt

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	"github.com/klphp/goravel/packages/types"
	"github.com/pkg/errors"
)

type JWT struct {
	Ctx *http.Context
}

type User struct {
	ID      uint   `gorm:"primaryKey"`
	TokenID string `json:"token_id"`
}

func NewJWT() *JWT {
	return &JWT{}
}

func (j *JWT) Context(ctx *http.Context) *JWT {
	j.Ctx = ctx
	return j
}

func (j *JWT) GenerateJwt(tokenable types.Tokenable, guard string, TokenId string) (string, error) {

	if j.Ctx == nil {
		return ``, errors.New(`context is nil`)
	}

	var user User
	user.ID = tokenable.GetID()
	user.TokenID = tokenable.GetStrID()

	token, err := facades.Auth(*j.Ctx).Guard(guard).Login(&user)

	if err != nil {
		println("jwt错误", err.Error())
		return ``, err
	}
	return token, nil
}
