package middleware

import (
	"crypto/md5"
	"encoding/hex"
	"strings"

	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	am "github.com/klphp/goravel/packages/admin/models"
	"github.com/klphp/goravel/packages/common"
	"github.com/klphp/goravel/packages/core/sanctum/contracts"
	sanctumFacades "github.com/klphp/goravel/packages/core/sanctum/facades"
	"github.com/klphp/goravel/packages/core/sanctum/models"
	um "github.com/klphp/goravel/packages/user/models"
)

// PersonalAccessToken 个人访问令牌
var sanctum contracts.Sanctum

// ResponseUnauthorized 返回未授权响应
func ResponseUnauthorized(ctx http.Context, message string) {
	ctx.Response().Status(401).Json(http.Json{
		"code":    401,
		"message": message,
	})
}

// SanctumVerify 验证Sanctum令牌
func SanctumVerify(token string) bool {
	//根据请求的token获取|前的id，再通过id查询库内token数据
	personalAccessToken := &models.PersonalAccessTokens{
		ID: sanctum.GetID(),
	}
	err := facades.Orm().Query().First(personalAccessToken)
	if !common.GormNotFound(err) && personalAccessToken.Token != `` {
		//验证请求token的md5值与库内的md5 token值是否一致
		h := md5.New()
		h.Write([]byte(sanctum.GetToken()))
		md5String := hex.EncodeToString(h.Sum(nil))
		if md5String == personalAccessToken.Token {
			return true
		}
	}
	return false
}

// AuthSanctum 使用Sanctum进行API认证的中间件
func AuthSanctum(guard string) http.Middleware {
	return func(ctx http.Context) {

		authorization := ctx.Request().Header(`Authorization`, ``)
		bearer := `Bearer `
		if authorization != `` && strings.HasPrefix(authorization, bearer) {
			tokenString := authorization[len(bearer):]

			sanctum = sanctumFacades.Sanctum().Context(ctx)
			//1、验证token格式是否正确
			if sanctum.VerifyTokenFormat(tokenString) {
				token := sanctum.GetToken()

				//解析token
				payload, err := facades.Auth(ctx).Guard(guard).Parse(token)
				//令牌过期或格式不正确
				if err != nil {
					ResponseUnauthorized(ctx, err.Error())
					return
				}
				//判断guard是否一致
				if payload.Guard != guard {
					ResponseUnauthorized(ctx, `身份验证失败.`)
					return
				}

				//验证token加密后的md5值是否有效
				if SanctumVerify(tokenString) {
					if guard == `admin` {
						//管理员登录
						var user am.Admin

						facades.Auth(ctx).Guard(guard).User(&user)
						ctx.WithValue(`user`, user)
					} else {
						var user um.User
						facades.Auth(ctx).Guard(guard).User(&user)
						ctx.WithValue(`user`, user)
					}
					ctx.Request().Next()
					return
				}
			}

			ResponseUnauthorized(ctx, `Unauthorized.`)
		} else {
			ResponseUnauthorized(ctx, `token verification failed.`)
		}

	}
}
