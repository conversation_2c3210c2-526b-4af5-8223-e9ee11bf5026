package middleware

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
)

func AutoTransaction() http.Middleware {
	return func(ctx http.Context) {
		method := ctx.Request().Method()
		if method != "GET" {
			query, _ := facades.Orm().Query().Begin()
			ctx.WithValue("query", query)
		} else {
			query := facades.Orm().Query()
			ctx.WithValue("query", query)
		}
		ctx.Request().Next()
	}
}
