package middleware

// import (
// 	"github.com/goravel/framework/contracts/http"
// 	"github.com/goravel/framework/facades"
// 	"github.com/klphp/goravel/packages/core/sanctum/models"
// )

// // Unauthorized 返回未授权响应
// func Unauthorized(ctx http.Context, message string) {
// 	ctx.Response().Status(403).Json(http.Json{
// 		"code":    403,
// 		"message": message,
// 	})
// }

// // Ability 使用Sanctum进行API认证的中间件
// func Ability(guard string, abilities ...string) http.Middleware {
// 	return func(ctx http.Context) {
// 		personalAccessToken := &models.PersonalAccessTokens{
// 			ID: sanctumComponent.GetID(),
// 		}
// 		// 根据tokenable_type确定用户类型
// 		var userModel interface{}
// 		var err2 error

// 		// 根据tokenable_type确定用户类型并获取用户信息
// 		switch personalAccessToken.TokenableType {
// 		case "*models.User", "*github.com/klphp/goravel/packages/user/models.User":
// 			// 用户模型
// 			err2 = facades.Auth(ctx).Guard(guard).User(&userModel)
// 		case "*models.Admin", "*github.com/klphp/goravel/packages/admin/models.Admin":
// 			// 管理员模型
// 			err2 = facades.Auth(ctx).Guard(guard).User(&userModel)
// 		default:
// 			ResponseUnauthorized(ctx, `未知的用户类型.`)
// 			return
// 		}

// 		if err2 != nil {
// 			ResponseUnauthorized(ctx, `身份信息解析失败.`)
// 			return
// 		}

// 		// 验证abilities
// 		if len(abilities) > 0 {
// 			// 验证token是否具有所需的abilities
// 			for _, ability := range abilities {
// 				if !sanctumComponent.Can(ability) {
// 					ResponseUnauthorized(ctx, `权限不足.`)
// 					return
// 				}
// 			}
// 		}
// 		ctx.Request().Next()
// 		return

// 	}
// }
