// common_crud.go

package common

import (
	"github.com/klphp/goravel/packages/core/response/facades"

	"github.com/goravel/framework/contracts/http"
)

type CRUDInterface[T any] interface {
	SetSort(sort string) CRUDInterface[T]
	Pages(page, limit int) ([]T, error)
	All(where ...*T) ([]T, error)
	Find(model *T) *T
	Create(model *T) (*T, error)
	Save(model *T) (*T, error)
	Pluck(model *T, field string, values any) error
	Delete(model *T) bool
	ForceDelete(model *T) bool
	Restore(model *T) bool
	DeleteAll(model *T, field, value any) bool
	Exists(model *T) bool
}

// CommonCRUD 通用 CRUD 操作结构体
type CommonCRUD[T any] struct {
	Ctx  http.Context
	Sort string
}

// SetSort 配置排序
func (a *CommonCRUD[T]) SetSort(sort string) CRUDInterface[T] {
	a.Sort = sort
	return a
}

// ClearSort 清除排序
func (a *CommonCRUD[T]) clearSort() {
	a.Sort = ""
}

// Pages
func (a *CommonCRUD[T]) Pages(page, limit int) ([]T, error) {
	resp := facades.Response().Context(a.Ctx)
	var models []T
	var total int64
	q := resp.Query()
	if a.Sort != "" {
		q.OrderBy(a.Sort)
	}
	err := q.Paginate(page, limit, &models, &total)
	a.clearSort()
	if err != nil {
		return nil, err
	}
	return models, nil
}

// All
func (a *CommonCRUD[T]) All(where ...*T) ([]T, error) {
	resp := facades.Response().Context(a.Ctx)
	var models []T
	q := resp.Query()
	if a.Sort != "" {
		q = q.Order(a.Sort)
	}
	if len(where) > 0 && where[0] != nil {
		q = q.Where(where[0])
	}
	err := q.Get(&models)
	a.clearSort()
	if err != nil {
		return nil, err
	}
	return models, nil
}

// Find
func (a *CommonCRUD[T]) Find(model *T) *T {
	resp := facades.Response().Context(a.Ctx)
	q := resp.Query()
	if a.Sort != "" {
		q.OrderBy(a.Sort)
	}
	err := q.First(model)
	a.clearSort()
	if err != nil {
		return nil
	}
	return model
}

// Create
func (a *CommonCRUD[T]) Create(model *T) (*T, error) {
	resp := facades.Response().Context(a.Ctx)
	err := resp.Query().Create(model)
	if err != nil {
		return nil, err
	}
	return model, nil
}

// Save 保存数据
func (a *CommonCRUD[T]) Save(model *T) (*T, error) {
	resp := facades.Response().Context(a.Ctx)
	err := resp.Query().Save(model)
	if err != nil {
		return nil, err
	}
	return model, nil
}

// Pluck 单列查询
func (a *CommonCRUD[T]) Pluck(model *T, field string, values any) error {
	resp := facades.Response().Context(a.Ctx)
	err := resp.Query().Model(model).Pluck(field, values)
	if err != nil {
		return err
	}
	return nil
}

// Delete 删除
func (a *CommonCRUD[T]) Delete(model *T) bool {
	resp := facades.Response().Context(a.Ctx)
	_, err := resp.Query().Delete(model)
	if err != nil {
		return false
	} else {
		return true
	}
}

// ForceDelete 强制删除（带软删除的数据）
func (a *CommonCRUD[T]) ForceDelete(model *T) bool {
	resp := facades.Response().Context(a.Ctx)
	_, err := resp.Query().ForceDelete(model)
	if err != nil {
		return false
	} else {
		return true
	}
}

// Restore 恢复软删除
func (a *CommonCRUD[T]) Restore(model *T) bool {
	resp := facades.Response().Context(a.Ctx)
	_, err := resp.Query().WithTrashed().Restore(model)
	if err != nil {
		return false
	} else {
		return true
	}
}

// DeleteAll 多条删除
func (a *CommonCRUD[T]) DeleteAll(model *T, field, value any) bool {
	resp := facades.Response().Context(a.Ctx)
	_, err := resp.Query().Where(field, value).Delete(model)
	if err != nil {
		return false
	} else {
		return true
	}
}

// Exists 检查数据是否存在
func (a *CommonCRUD[T]) Exists(model *T) bool {
	resp := facades.Response().Context(a.Ctx)
	exists := false
	err := resp.Query().Model(model).Where(model).Exists(&exists)
	if err != nil {
		return false
	}
	return exists
}
