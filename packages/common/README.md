# Common 通用包

## CommonCRUD 通用 CRUD 扩展

`CommonCRUD` 是一个基于泛型的通用 CRUD 操作扩展，可以帮助您快速实现数据模型的增删改查功能，减少重复代码。

### 功能特点

- 基于 Go 泛型实现，支持任意模型类型
- 提供完整的 CRUD 操作接口
- 支持分页查询、排序、条件过滤等功能
- 支持软删除和恢复操作
- 易于集成到现有项目中

### CRUDInterface 接口

`CommonCRUD` 实现了 `CRUDInterface` 泛型接口，该接口定义了以下方法：

```go
type CRUDInterface[T any] interface {
	SetSort(sort string) CRUDInterface[T]     // 设置排序
	Pages(page, limit int) ([]T, error)       // 分页查询
	All(where ...*T) ([]T, error)             // 获取所有记录
	Find(model *T) *T                         // 查找单条记录
	Create(model *T) (*T, error)              // 创建记录
	Save(model *T) (*T, error)                // 保存记录
	Pluck(model *T, field string, values any) error // 获取单列数据
	Delete(model *T) bool                     // 删除记录
	ForceDelete(model *T) bool                // 强制删除（含软删除的记录）
	Restore(model *T) bool                    // 恢复软删除的记录
	DeleteAll(model *T, field, value any) bool // 批量删除
	Exists(model *T) bool                     // 检查记录是否存在
}
```

### 使用方法

#### 1. 创建模型

首先，定义您的数据模型，例如：

```go
package models

import "github.com/goravel/framework/database/orm"

type Groups struct {
	orm.Model
	Name string `form:"name" json:"name"`
}
```

#### 2. 创建服务结构体

创建一个包含 `CommonCRUD` 的服务结构体：

```go
package groups

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/klphp/goravel/packages/common"
	"github.com/klphp/goravel/packages/groups/models"
)

type Groups struct {
	*common.CommonCRUD[models.Groups]
}

func NewGroups() *Groups {
	return &Groups{}
}

func (g *Groups) Context(ctx http.Context) *Groups {
	g.CommonCRUD = &common.CommonCRUD[models.Groups]{Ctx: ctx}
	return g
}
```

#### 3. 定义接口（可选）

为了更好的依赖注入，您可以定义一个接口：

```go
package contracts

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/klphp/goravel/packages/common"
	"github.com/klphp/goravel/packages/groups"
	"github.com/klphp/goravel/packages/groups/models"
)

type Groups interface {
	common.CRUDInterface[models.Groups]
	Context(ctx http.Context) *groups.Groups
}
```

#### 4. 在控制器中使用

```go
func (c *GroupsController) Index(ctx http.Context) {
	// 初始化服务
	groupsService := groups.NewGroups().Context(ctx)

	// 设置排序并分页查询
	groupsList, err := groupsService.SetSort("id desc").Pages(1, 10)
	if err != nil {
		// 处理错误
	}

	// 返回数据
	return ctx.Success().Data(groupsList)
}

func (c *GroupsController) Store(ctx http.Context) {
	// 创建模型
	group := &models.Groups{
		Name: ctx.Request().Input("name"),
	}

	// 保存数据
	result, err := groups.NewGroups().Context(ctx).Create(group)
	if err != nil {
		// 处理错误
	}

	// 返回结果
	return ctx.Success().Data(result)
}
```

### 常见问题

#### 如何自定义查询条件？

使用 `All` 方法时可以传入条件模型：

```go
condition := &models.Groups{Name: "测试组"}
groupsList, err := groupsService.All(condition)
```

#### 如何处理软删除？

使用 `ForceDelete` 方法可以强制删除带软删除的记录，使用 `Restore` 方法可以恢复软删除的记录：

```go
// 强制删除
success := groupsService.ForceDelete(group)

// 恢复软删除
success := groupsService.Restore(group)
```

#### 如何批量删除？

使用 `DeleteAll` 方法可以批量删除符合条件的记录：

```go
// 删除所有name为"测试组"的记录
success := groupsService.DeleteAll(&models.Groups{}, "name", "测试组")
```

### 完整示例代码

以下是一个完整的控制器示例，展示了如何使用 `CommonCRUD` 的所有功能：

```go
package controllers

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/database/orm"
	"github.com/klphp/goravel/packages/core/response/facades"
	"github.com/klphp/goravel/packages/groups/facades"
	"github.com/klphp/goravel/packages/groups/models"
)

type GroupsController struct {}

func NewGroupsController() *GroupsController {
	return &GroupsController{}
}

func (c *GroupsController) Index(ctx http.Context) http.Response {
	// 初始化响应服务
	resp := facades.Response().Context(ctx)

	// 初始化分组服务
	groups := facades.Groups().Context(ctx)

	// 1. 创建记录
	newGroup, err := groups.Create(&models.Groups{
		Name: "测试分组",
	})
	if err != nil {
		return resp.Error(err)
	}
	fmt.Println("创建成功:", newGroup)

	// 2. 查询单条记录
	group := groups.Find(&models.Groups{Model: orm.Model{ID: 2}})
	fmt.Println("查询单条记录:", group)

	// 3. 更新记录
	group.Name = "更新后的名称"
	updatedGroup, err := groups.Save(group)
	if err != nil {
		return resp.Error(err)
	}
	fmt.Println("更新成功:", updatedGroup)

	// 4. 查询单列数据
	var ids []uint
	err = groups.Pluck(&models.Groups{}, "id", &ids)
	if err != nil {
		return resp.Error(err)
	}
	fmt.Println("ID列表:", ids)

	// 5. 检查记录是否存在
	exists := groups.Exists(&models.Groups{
		Model: orm.Model{ID: 1},
		Name:  "测试分组",
	})
	fmt.Println("记录是否存在:", exists)

	// 6. 条件查询所有记录
	condition := &models.Groups{Name: "测试分组"}
	groupList, err := groups.All(condition)
	if err != nil {
		return resp.Error(err)
	}
	fmt.Println("条件查询结果:", groupList)

	// 7. 排序并查询所有记录
	allGroups, err := groups.SetSort("id desc").All()
	if err != nil {
		return resp.Error(err)
	}
	fmt.Println("所有记录(降序):", allGroups)

	// 8. 分页查询
	pageGroups, err := groups.SetSort("id asc").Pages(1, 10)
	if err != nil {
		return resp.Error(err)
	}
	fmt.Println("分页查询结果:", pageGroups)

	// 9. 软删除记录
	deleteSuccess := groups.Delete(&models.Groups{Model: orm.Model{ID: 1}})
	fmt.Println("软删除结果:", deleteSuccess)

	// 10. 恢复软删除的记录
	restoreSuccess := groups.Restore(&models.Groups{Model: orm.Model{ID: 1}})
	fmt.Println("恢复软删除结果:", restoreSuccess)

	// 11. 强制删除记录(包括软删除的记录)
	forceDeleteSuccess := groups.ForceDelete(&models.Groups{Model: orm.Model{ID: 3}})
	fmt.Println("强制删除结果:", forceDeleteSuccess)

	// 12. 批量删除记录
	deleteAllSuccess := groups.DeleteAll(&models.Groups{}, "name", "测试分组")
	fmt.Println("批量删除结果:", deleteAllSuccess)

	// 返回所有记录
	return resp.Success(allGroups)
}

func (c *GroupsController) Store(ctx http.Context) http.Response {
	// 初始化响应服务
	resp := facades.Response().Context(ctx)

	// 创建模型
	group := &models.Groups{
		Name: ctx.Request().Input("name"),
	}

	// 保存数据
	result, err := facades.Groups().Context(ctx).Create(group)
	if err != nil {
		return resp.Error(err)
	}

	// 返回结果
	return resp.Success(result)
}

func (c *GroupsController) Update(ctx http.Context) http.Response {
	// 初始化响应服务
	resp := facades.Response().Context(ctx)

	// 获取ID
	id := ctx.Request().Input("id")

	// 查找记录
	groups := facades.Groups().Context(ctx)
	group := groups.Find(&models.Groups{Model: orm.Model{ID: uint(ctx.Request().InputInt("id"))}})
	if group == nil {
		return resp.Error(fmt.Errorf("记录不存在: %s", id))
	}

	// 更新字段
	group.Name = ctx.Request().Input("name")

	// 保存更新
	result, err := groups.Save(group)
	if err != nil {
		return resp.Error(err)
	}

	// 返回结果
	return resp.Success(result)
}

func (c *GroupsController) Destroy(ctx http.Context) http.Response {
	// 初始化响应服务
	resp := facades.Response().Context(ctx)

	// 获取ID
	id := uint(ctx.Request().InputInt("id"))

	// 删除记录
	success := facades.Groups().Context(ctx).Delete(&models.Groups{Model: orm.Model{ID: id}})

	// 返回结果
	return resp.Success(map[string]interface{}{
		"success": success,
		"message": "删除操作已完成",
	})
}
```

这个完整示例展示了如何在控制器中使用 `CommonCRUD` 的所有功能，包括：

1. 创建记录 (Create)
2. 查询单条记录 (Find)
3. 更新记录 (Save)
4. 查询单列数据 (Pluck)
5. 检查记录是否存在 (Exists)
6. 条件查询所有记录 (All with condition)
7. 排序并查询所有记录 (SetSort + All)
8. 分页查询 (SetSort + Pages)
9. 软删除记录 (Delete)
10. 恢复软删除的记录 (Restore)
11. 强制删除记录 (ForceDelete)
12. 批量删除记录 (DeleteAll)

同时还提供了标准的 RESTful 控制器方法实现，包括：

- Index: 列出所有记录
- Store: 创建新记录
- Update: 更新现有记录
- Destroy: 删除记录
