package rbac

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	facades2 "github.com/klphp/goravel/packages/core/response/facades"
	"github.com/klphp/goravel/packages/rbac/models"
	"github.com/klphp/goravel/packages/rbac/requests"
)

// StoreMenu godoc
// @Summary 创建菜单
// @Description 创建一个新的菜单
// @Tags RBAC-菜单管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param menu body requests.StoreMenuRequest true "菜单信息"
// @Success 200 {object} SwaggerMenu "创建成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/menu [post]
func (a *RBAC) StoreMenu(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)
	// 绑定参数
	var storeMenu requests.StoreMenuRequest
	errors, _ := ctx.Request().ValidateRequest(&storeMenu)
	if errors != nil {
		return resp.Error(fmt.Errorf("参数错误: %s", errors.One()))
	}

	// 创建菜单对象
	menu := &models.Menu{
		Name:      storeMenu.Name,
		Route:     &storeMenu.Route,
		Order:     uint(storeMenu.Order),
		Icon:      &storeMenu.Icon,
		GuardName: storeMenu.GuardName,
	}

	// 只有当ParentID不为0时才设置，否则设置为nil
	if storeMenu.ParentID != 0 {
		menu.ParentID = &storeMenu.ParentID
	}

	// 验证菜单是否存在
	var count int64
	err := resp.Query().Model(&models.Menu{}).Where("name = ? AND guard_name = ?", menu.Name, menu.GuardName).Count(&count)
	if err != nil {
		return resp.Error(err)
	}
	if count > 0 {
		return resp.Error(fmt.Errorf("菜单已存在"))
	}

	// 创建菜单
	createdMenu, err := a.menuService.Create(menu)
	if err != nil {
		return resp.Error(err)
	}
	return resp.Success(createdMenu)
}

// GetMenus godoc
// @Summary 获取菜单列表
// @Description 获取所有菜单的树形结构列表
// @Tags RBAC-菜单管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param guard_name query string false "守卫名称" default(web)
// @Success 200 {array} SwaggerMenu "菜单树形列表"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/menus [get]
func (a *RBAC) GetMenus(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取守卫名称
	guardName := ctx.Request().Query("guard_name", "web")

	// 获取菜单树
	menuTree, err := a.menuService.GetMenuTree(guardName)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(menuTree)
}

// GetMenu godoc
// @Summary 获取菜单详情
// @Description 获取指定菜单的详细信息
// @Tags RBAC-菜单管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param id path int true "菜单ID"
// @Success 200 {object} SwaggerMenu "菜单详情"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "菜单不存在"
// @Router /admin/auth/menu/{id} [get]
func (a *RBAC) GetMenu(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取菜单ID
	id := ctx.Request().RouteInt("id")
	if id <= 0 {
		return resp.Error(fmt.Errorf("无效的菜单ID"))
	}

	// 获取菜单
	menu, err := a.menuService.Get(uint(id))
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(menu)
}

// UpdateMenu godoc
// @Summary 更新菜单
// @Description 更新指定菜单的信息
// @Tags RBAC-菜单管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param id path int true "菜单ID"
// @Param menu body requests.UpdateMenuRequest true "菜单信息"
// @Success 200 {object} SwaggerMenu "更新成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "菜单不存在"
// @Router /admin/auth/menu/{id} [put]
func (a *RBAC) UpdateMenu(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取菜单ID
	id := ctx.Request().RouteInt("id")
	if id <= 0 {
		return resp.Error(fmt.Errorf("无效的菜单ID"))
	}

	// 检查菜单是否存在
	menu, err := a.menuService.Get(uint(id))
	if err != nil {
		return resp.Error(err)
	}

	// 绑定参数
	var updateMenu requests.UpdateMenuRequest
	_ = ctx.Request().Bind(&updateMenu)

	// 确保请求中的ID与路由参数一致
	updateMenu.ID = uint(id)

	// 检查名称是否已被其他菜单使用
	var count int64
	err = resp.Query().Model(&models.Menu{}).Where(
		"name = ? AND guard_name = ? AND id != ?",
		updateMenu.Name, updateMenu.GuardName, id,
	).Count(&count)
	if err != nil {
		return resp.Error(err)
	}
	if count > 0 {
		return resp.Error(fmt.Errorf("菜单名称已被使用"))
	}

	// 更新菜单
	menu.Name = updateMenu.Name
	menu.Route = &updateMenu.Route
	menu.Order = uint(updateMenu.Order)
	menu.Icon = &updateMenu.Icon
	menu.GuardName = updateMenu.GuardName

	// 只有当ParentID不为0时才设置，否则设置为nil
	if updateMenu.ParentID != 0 {
		menu.ParentID = &updateMenu.ParentID
	} else {
		menu.ParentID = nil
	}

	err = resp.Query().Save(menu)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(menu)
}

// DeleteMenu godoc
// @Summary 删除菜单
// @Description 删除指定的菜单
// @Tags RBAC-菜单管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param id path int true "菜单ID"
// @Success 200 {object} map[string]interface{} "删除成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "菜单不存在"
// @Router /admin/auth/menu/{id} [delete]
func (a *RBAC) DeleteMenu(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取菜单ID
	id := ctx.Request().RouteInt("id")
	if id <= 0 {
		return resp.Error(fmt.Errorf("无效的菜单ID"))
	}

	// 删除菜单
	err := a.menuService.Delete(uint(id))
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(nil)
}

// GetUserMenus godoc
// @Summary 获取用户菜单
// @Description 获取指定用户有权限访问的菜单树形结构
// @Tags RBAC-菜单管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param model_id query int true "用户ID"
// @Param model_type query string true "用户类型"
// @Param guard_name query string false "守卫名称" default(web)
// @Success 200 {array} SwaggerMenu "用户菜单树形列表"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/user-menus [get]
func (a *RBAC) GetUserMenus(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取用户ID和类型
	modelID := ctx.Request().QueryInt("model_id", 0)
	modelType := ctx.Request().Query("model_type", "")
	guardName := ctx.Request().Query("guard_name", "web")

	if modelID <= 0 || modelType == "" {
		return resp.Error(fmt.Errorf("无效的用户ID或类型"))
	}

	// 获取用户菜单
	menuTree, err := a.menuService.GetUserMenus(uint(modelID), modelType, guardName, a.permissionService)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(menuTree)
}
