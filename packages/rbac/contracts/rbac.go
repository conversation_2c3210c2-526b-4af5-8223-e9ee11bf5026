package contracts

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/klphp/goravel/packages/rbac/models"
)

// Rbac 定义了RBAC权限管理系统的接口
type RBAC interface {
	// 角色管理
	CreateRole(name string, guardName string) (*models.Role, error)
	GetRole(id uint) (*models.Role, error)
	DeleteRole(id uint) error

	// 权限管理
	CreatePermission(name string, guardName string) (*models.Permission, error)
	GetPermission(id uint) (*models.Permission, error)
	DeletePermission(id uint) error

	// 角色分配
	AssignRoleToUser(roleID uint, modelID uint, modelType string) error
	RemoveRoleFromUser(roleID uint, modelID uint, modelType string) error

	// 权限分配
	AssignPermissionToRole(permissionID uint, roleID uint) error
	RemovePermissionFromRole(permissionID uint, roleID uint) error
	AssignPermissionToUser(permissionID uint, modelID uint, modelType string) error
	RemovePermissionFromUser(permissionID uint, modelID uint, modelType string) error

	// 权限检查
	HasRole(modelID uint, modelType string, roleName string, guardName string) (bool, error)
	HasPermission(modelID uint, modelType string, permissionName string, guardName string) (bool, error)

	// 菜单管理
	CreateMenu(menu *models.Menu) (*models.Menu, error)
	GetMenu(id uint) (*models.Menu, error)
	DeleteMenu(id uint) error
	GetMenusByGuard(guardName string) ([]*models.Menu, error)
	GetMenuTree(guardName string) ([]*MenuTree, error)
	GetUserMenus(modelID uint, modelType string, guardName string) ([]*MenuTree, error)

	// 中间件
	Middleware(permission string, guardName string) http.Middleware
}

// MenuTree 表示菜单的树形结构
type MenuTree struct {
	ID       uint        `json:"id"`
	Name     string      `json:"name"`
	Route    *string     `json:"route"`
	Icon     *string     `json:"icon"`
	Order    uint        `json:"order"`
	Children []*MenuTree `json:"children"`
}
