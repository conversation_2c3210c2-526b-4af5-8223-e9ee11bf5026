package rbac

// 这个文件包含 Swagger 文档生成所需的类型定义

// 定义 orm.Model 结构体，用于 Swagger 文档生成
// @Description GORM 模型基础字段
type Model struct {
	// 主键ID
	ID uint `json:"id"`
	// 创建时间
	CreatedAt string `json:"created_at"`
	// 更新时间
	UpdatedAt string `json:"updated_at"`
	// 删除时间（软删除）
	DeletedAt *string `json:"deleted_at,omitempty"`
}

// 定义 Role 结构体，用于 Swagger 文档生成
// @Description 角色模型
type SwaggerRole struct {
	// 主键ID
	ID uint `json:"id"`
	// 创建时间
	CreatedAt string `json:"created_at"`
	// 更新时间
	UpdatedAt string `json:"updated_at"`
	// 角色名称
	Name string `json:"name"`
	// 认证守卫名称
	GuardName string `json:"guard_name"`
	// 关联的权限列表
	Permissions []SwaggerPermission `json:"permissions,omitempty"`
}

// 定义 Permission 结构体，用于 Swagger 文档生成
// @Description 权限模型
type SwaggerPermission struct {
	// 主键ID
	ID uint `json:"id"`
	// 创建时间
	CreatedAt string `json:"created_at"`
	// 更新时间
	UpdatedAt string `json:"updated_at"`
	// 权限名称
	Name string `json:"name"`
	// 认证守卫名称
	GuardName string `json:"guard_name"`
}

// 定义 Menu 结构体，用于 Swagger 文档生成
// @Description 菜单模型
type SwaggerMenu struct {
	// 主键ID
	ID uint `json:"id"`
	// 创建时间
	CreatedAt string `json:"created_at"`
	// 更新时间
	UpdatedAt string `json:"updated_at"`
	// 父菜单ID
	ParentID *uint `json:"parent_id,omitempty"`
	// 菜单名称
	Name string `json:"name"`
	// 菜单路由
	Route *string `json:"route,omitempty"`
	// 菜单图标
	Icon *string `json:"icon,omitempty"`
	// 菜单排序
	Order uint `json:"order"`
	// 认证守卫名称
	GuardName string `json:"guard_name"`
	// 子菜单列表
	Children []SwaggerMenu `json:"children,omitempty"`
}
