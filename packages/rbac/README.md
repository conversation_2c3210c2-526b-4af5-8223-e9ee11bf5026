# rbac - RBAC 权限管理系统

packages/rbac 是基于 goravel 扩展实现 rbac 功能。

## 功能特性

- **角色 (Roles)**: 将权限分组，方便管理。一个用户可以拥有多个角色。
- **权限 (Permissions)**: 定义了具体的动作或能力。
- **角色-权限关联**: 定义了哪些角色拥有哪些权限。
- **用户-角色关联**: 将角色分配给用户。
- **直接权限**: 允许直接给用户分配特定的权限，而无需通过角色。这在处理特殊用户的例外情况时很有用。
- **多态关联 (morphs)**: 使得权限和角色可以关联到任何模型，不仅仅是 User 模型，这为更复杂的组织结构和实体提供了支持。
- **认证守卫 (guard_name)**: 允许你在不同的认证场景下使用不同的权限和角色集合。
- **菜单控制**: 使用 RBAC（Role-Based Access Control）来控制菜单的显示。

## 数据库表结构

数据库迁移文件位于 `database/migrations` 目录中：

- `M20250506073840CreaterbacRolesTable`: 角色表
- `M20250506073908CreaterbacPermissionsTable`: 权限表
- `M20250506073937CreaterbacRoleHasPermissionsTable`: 角色-权限关联表
- `M20250506074008CreaterbacModelHasRolesTable`: 模型-角色关联表
- `M20250506074108CreaterbacModelHasPermissionsTable`: 模型-权限关联表
- `M20250506075236CreaterbacMenusTable`: 菜单表

## 使用方法

### 1. 注册服务提供者

在 `config/app.go` 文件中注册 rbac 服务提供者：

```go
"providers": []foundation.ServiceProvider{
    // ...
    &rbac.ServiceProvider{},
},
```

### 2. 使用 rbac 门面

```go
import "github.com/klphp/goravel/packages/rbac/facades"

// 创建角色
role, err := facades.rbac().CreateRole("admin", "admin")

// 创建权限
permission, err := facades.rbac().CreatePermission("create-user", "admin")

// 将权限分配给角色
err = facades.rbac().AssignPermissionToRole(permission.ID, role.ID)

// 将角色分配给用户
err = facades.rbac().AssignRoleToUser(role.ID, user.ID, "App\\Models\\User")

// 检查用户是否拥有角色
hasRole, err := facades.rbac().HasRole(user.ID, "App\\Models\\User", "admin", "admin")

// 检查用户是否拥有权限
hasPermission, err := facades.rbac().HasPermission(user.ID, "App\\Models\\User", "create-user", "admin")
```

### 3. 使用权限中间件

在路由中使用权限中间件：

```go
import (
    "github.com/goravel/framework/facades"
    rbacFacades "github.com/klphp/goravel/packages/rbac/facades"
)

func RegisterRoutes() {
    facades.Route().Middleware(rbacFacades.RBAC().Middleware("create-user", "admin")).Post("/users", userController.Create)
}
```

### 4. 菜单管理

```go
// 创建菜单
menu := &models.Menu{
    Name:      "用户管理",
    Route:     facades.Config().GetString("app.url") + "/users",
    ParentID:  nil, // 顶级菜单
    Order:     1,
    Icon:      "user-icon",
    GuardName: "admin",
}

createdMenu, err := facades.rbac().CreateMenu(menu)

// 获取菜单树
menuTree, err := facades.rbac().GetMenuTree("admin")

// 获取用户有权限访问的菜单
userMenus, err := facades.rbac().GetUserMenus(user.ID, "App\\Models\\User", "admin")
```

## 注意事项

1. 在使用多态关联时，`modelType` 参数应该是完整的类名，包括命名空间，例如 `App\Models\User`。
2. 菜单权限检查默认使用 `"访问:{路由}"` 格式的权限名称，确保在创建权限时使用相同的命名规则。
3. 不同的守卫（guard）可以有不同的权限和角色集合，确保在创建角色和权限时指定正确的守卫名称。
