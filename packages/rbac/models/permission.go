package models

import (
	"github.com/goravel/framework/database/orm"
)

// Permission 对应 auth_permissions 表
type Permission struct {
	orm.Model
	Name      string `gorm:"uniqueIndex:idx_name_guard_name;comment:权限名称" json:"name"`
	GuardName string `gorm:"uniqueIndex:idx_name_guard_name;comment:认证守卫名称" json:"guard_name"`

	// 关联关系
	Roles []Role `gorm:"many2many:auth_role_has_permissions;foreignKey:ID;joinForeignKey:PermissionID;References:ID;joinReferences:RoleID" json:"roles,omitempty"`
	// 多态关联 - 这里不直接定义，因为模型类型是动态的
}

func (p *Permission) TableName() string {
	return "auth_permissions"
}
