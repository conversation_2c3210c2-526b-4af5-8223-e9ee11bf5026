package models

// ModelHasPermission 对应 auth_model_has_permissions 表
type ModelHasPermission struct {
	PermissionID uint   `gorm:"primaryKey;autoIncrement:false;comment:权限ID" json:"permission_id"`
	ModelID      uint   `gorm:"primaryKey;autoIncrement:false;comment:模型ID" json:"model_id"`
	ModelType    string `gorm:"primaryKey;autoIncrement:false;comment:模型类型" json:"model_type"`

	// Define relationships if needed, for example:
	Permission Permission `gorm:"foreignKey:PermissionID"`
}

func (mhp *ModelHasPermission) TableName() string {
	return "auth_model_has_permissions"
}
