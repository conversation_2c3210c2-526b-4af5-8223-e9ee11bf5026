package models

import (
	"github.com/goravel/framework/database/orm"
)

// Menu 对应 auth_menus 表
type Menu struct {
	orm.Model
	Name      string  `gorm:"comment:菜单名称" json:"name"`
	Route     *string `gorm:"comment:菜单对应的路由或链接" json:"route"`
	ParentID  *uint   `gorm:"comment:父级菜单 ID" json:"parent_id"`
	Order     uint    `gorm:"default:0;comment:菜单排序" json:"order"`
	Icon      *string `gorm:"comment:菜单图标" json:"icon"`
	GuardName string  `gorm:"comment:认证守卫名称" json:"guard_name"`

	// Relationships
	Parent   *Menu   `gorm:"foreignKey:ParentID"`
	Children []*Menu `gorm:"foreignKey:ParentID"`
}

func (m *Menu) TableName() string {
	return "auth_menus"
}
