package models

import (
	"github.com/goravel/framework/database/orm"
)

// Role 对应 auth_roles 表
type Role struct {
	orm.Model
	Name      string `gorm:"uniqueIndex:idx_name_guard_name;comment:角色名称" json:"name"`
	GuardName string `gorm:"uniqueIndex:idx_name_guard_name;comment:认证守卫名称" json:"guard_name"`

	// 关联关系
	Permissions []Permission `gorm:"many2many:auth_role_has_permissions;foreignKey:ID;joinForeignKey:RoleID;References:ID;joinReferences:PermissionID" json:"permissions,omitempty"`
	// 多态关联 - 这里不直接定义，因为模型类型是动态的
}

func (r *Role) TableName() string {
	return "auth_roles"
}
