package models

// RoleHasPermission 对应 auth_role_has_permissions 表
type RoleHasPermission struct {
	PermissionID uint `gorm:"primaryKey;autoIncrement:false;comment:权限ID" json:"permission_id"`
	RoleID       uint `gorm:"primaryKey;autoIncrement:false;comment:角色ID" json:"role_id"`

	// Define relationships if needed, for example:
	Permission Permission `gorm:"foreignKey:PermissionID"`
	Role       Role       `gorm:"foreignKey:RoleID"`
}

func (rhp *RoleHasPermission) TableName() string {
	return "auth_role_has_permissions"
}
