package models

// ModelHasRole 对应 auth_model_has_roles 表
type ModelHasRole struct {
	RoleID    uint   `gorm:"primaryKey;autoIncrement:false;comment:角色ID" json:"role_id"`
	ModelID   uint   `gorm:"primaryKey;autoIncrement:false;comment:模型ID" json:"model_id"`
	ModelType string `gorm:"primaryKey;autoIncrement:false;comment:模型类型" json:"model_type"`

	// Define relationships if needed, for example:
	Role Role `gorm:"foreignKey:RoleID"`
}

func (mhr *ModelHasRole) TableName() string {
	return "auth_model_has_roles"
}
