package rbac

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	facades2 "github.com/klphp/goravel/packages/core/response/facades"
	"github.com/klphp/goravel/packages/rbac/models"
	"github.com/klphp/goravel/packages/rbac/requests"
)

// StoreRole godoc
// @Summary 创建角色
// @Description 创建一个新的角色
// @Tags RBAC-角色管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param role body requests.StoreRoleRequest true "角色信息"
// @Success 200 {object} SwaggerRole "创建成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/role [post]
func (a *RBAC) StoreRole(ctx http.Context) http.Response {

	resp := facades2.Response().Context(ctx)

	var storeRole requests.StoreRoleRequest
	errors, _ := ctx.Request().ValidateRequest(&storeRole)
	// 绑定参数
	if errors != nil {
		return resp.Error(fmt.Errorf("参数错误: %s", errors.One()))
	}
	role := &models.Role{
		Name:      storeRole.Name,
		GuardName: storeRole.GuardName,
	}

	// 验证角色是否存在
	var count int64
	err := resp.Query().Model(role).Where(role).Count(&count)
	if err != nil {
		return resp.Error(err)
	}
	if count > 0 {
		return resp.Error(fmt.Errorf("角色已存在"))
	}

	// 创建角色
	role, err = a.roleService.Create(role.Name, role.GuardName)
	if err != nil {
		return resp.Error(err)
	}
	return resp.Success(role)
}

// GetRoles godoc
// @Summary 获取角色列表
// @Description 获取所有角色的列表，支持分页和筛选
// @Tags RBAC-角色管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param page query int false "页码" default(1)
// @Param per_page query int false "每页数量" default(15)
// @Param name query string false "角色名称（模糊搜索）"
// @Param guard_name query string false "守卫名称"
// @Success 200 {object} map[string]interface{} "角色列表"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/roles [get]
func (a *RBAC) GetRoles(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取分页参数
	page := ctx.Request().QueryInt("page", 1)
	perPage := ctx.Request().QueryInt("per_page", 15)

	// 查询条件
	name := ctx.Request().Query("name", "")
	guardName := ctx.Request().Query("guard_name", "")

	// 构建查询
	query := resp.Query().Model(&models.Role{})

	// 添加过滤条件
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if guardName != "" {
		query = query.Where("guard_name = ?", guardName)
	}

	// 执行分页查询
	var roles []models.Role
	var total int64
	err := query.Paginate(page, perPage, &roles, &total)
	if err != nil {
		return resp.Error(err)
	}

	// 构建分页响应数据
	paginator := map[string]interface{}{
		"data":  roles,
		"total": total,
		"page":  page,
		"limit": perPage,
	}

	return resp.Success(paginator)
}

// ShowRole godoc
// @Summary 获取角色详情
// @Description 获取指定角色的详细信息，包括角色拥有的权限
// @Tags RBAC-角色管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param id path int true "角色ID"
// @Success 200 {object} map[string]interface{} "角色详情"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "角色不存在"
// @Router /admin/auth/role/{id} [get]
func (a *RBAC) ShowRole(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取角色ID
	id := ctx.Request().RouteInt("id")
	if id <= 0 {
		return resp.Error(fmt.Errorf("无效的角色ID"))
	}

	// 获取角色
	role, err := a.roleService.Get(uint(id))
	if err != nil {
		return resp.Error(err)
	}

	// 获取角色的权限
	var permissions []models.Permission
	err = resp.Query().Raw(`
		SELECT auth_permissions.* FROM auth_permissions
		JOIN auth_role_has_permissions ON auth_permissions.id = auth_role_has_permissions.permission_id
		WHERE auth_role_has_permissions.role_id = ?
	`, id).Find(&permissions)
	if err != nil {
		return resp.Error(err)
	}

	// 构建响应数据
	responseData := map[string]interface{}{
		"role":        role,
		"permissions": permissions,
	}

	return resp.Success(responseData)
}

// UpdateRole godoc
// @Summary 更新角色
// @Description 更新指定角色的信息
// @Tags RBAC-角色管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param id path int true "角色ID"
// @Param role body requests.UpdateRoleRequest true "角色信息"
// @Success 200 {object} SwaggerRole "更新成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "角色不存在"
// @Router /admin/auth/role/{id} [put]
func (a *RBAC) UpdateRole(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	var updateRole requests.UpdateRoleRequest
	errors, _ := ctx.Request().ValidateRequest(&updateRole)
	// 绑定参数
	if errors != nil {
		return resp.Error(fmt.Errorf("参数错误: %s", errors.One()))
	}

	// 检查角色是否存在
	role, err := a.roleService.Get(updateRole.ID)
	if err != nil {
		return resp.Error(err)
	}

	// 检查名称是否已被其他角色使用
	var count int64
	err = resp.Query().Model(&models.Role{}).Where(
		"name = ? AND guard_name = ? AND id != ?",
		updateRole.Name, updateRole.GuardName, updateRole.ID,
	).Count(&count)
	if err != nil {
		return resp.Error(err)
	}
	if count > 0 {
		return resp.Error(fmt.Errorf("角色名称已被使用"))
	}

	// 更新角色
	role.Name = updateRole.Name
	role.GuardName = updateRole.GuardName

	err = resp.Query().Save(role)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(role)
}

// DestroyRole godoc
// @Summary 删除角色
// @Description 删除指定的角色
// @Tags RBAC-角色管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param id path int true "角色ID"
// @Success 200 {object} map[string]interface{} "删除成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "角色不存在"
// @Router /admin/auth/role/{id} [delete]
func (a *RBAC) DestroyRole(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取角色ID
	id := ctx.Request().RouteInt("id")
	if id <= 0 {
		return resp.Error(fmt.Errorf("无效的角色ID"))
	}

	// 删除角色
	err := a.roleService.Delete(uint(id))
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(nil)
}
