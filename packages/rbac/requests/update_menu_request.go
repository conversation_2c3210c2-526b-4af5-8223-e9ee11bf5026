package requests

import (
	"github.com/goravel/framework/contracts/http"
)

// UpdateMenuRequest 更新菜单请求
type UpdateMenuRequest struct {
	ID        uint   `form:"id" json:"id" binding:"required"`
	Name      string `form:"name" json:"name" binding:"required"`
	Route     string `form:"route" json:"route"`
	ParentID  uint   `form:"parent_id" json:"parent_id"`
	Order     int    `form:"order" json:"order"`
	Icon      string `form:"icon" json:"icon"`
	GuardName string `form:"guard_name" json:"guard_name" binding:"required"`
}

// Authorize 授权验证
func (r *UpdateMenuRequest) Authorize(ctx http.Context) error {
	return nil
}

// Rules 验证规则
func (r *UpdateMenuRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"id":         "required|numeric",
		"name":       "required|max:255",
		"guard_name": "required|max:255",
	}
}

// Messages 错误信息
func (r *UpdateMenuRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		"id.required":         "菜单ID不能为空",
		"id.numeric":          "菜单ID必须是数字",
		"name.required":       "菜单名称不能为空",
		"name.max":            "菜单名称不能超过255个字符",
		"guard_name.required": "守卫名称不能为空",
		"guard_name.max":      "守卫名称不能超过255个字符",
	}
}
