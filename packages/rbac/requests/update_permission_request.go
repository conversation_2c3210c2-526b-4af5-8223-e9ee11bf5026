package requests

import (
	"github.com/goravel/framework/contracts/http"
)

type UpdatePermissionRequest struct {
	ID        uint   `form:"id" json:"id"`
	Name      string `form:"name" json:"name"`
	GuardName string `form:"guard_name" json:"guard_name"`
}

func (r *UpdatePermissionRequest) Authorize(ctx http.Context) error {
	return nil
}

func (r *UpdatePermissionRequest) Filters(ctx http.Context) map[string]string {
	return map[string]string{
		"name":       "trim",
		"guard_name": "trim",
	}
}

func (r *UpdatePermissionRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"id":         "required|numeric",
		"name":       "required|max_len:255",
		"guard_name": "required|max_len:255",
	}
}

func (r *UpdatePermissionRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		"id.required":         "权限ID不能为空",
		"name.required":       "权限名称不能为空",
		"guard_name.required": "守卫名称不能为空",
	}
}

func (r *UpdatePermissionRequest) Attributes(ctx http.Context) map[string]string {
	return map[string]string{
		"id":         "权限ID",
		"name":       "权限名称",
		"guard_name": "守卫名称",
	}
}
