package requests

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/contracts/validation"
)

type StorePermissionRequest struct {
	Name      string `form:"name" json:"name"`
	GuardName string `form:"guard_name" json:"guard_name"`
}

func (r *StorePermissionRequest) Authorize(ctx http.Context) error {
	return nil
}

func (r *StorePermissionRequest) Filters(ctx http.Context) map[string]string {
	return map[string]string{
		"name":       "trim",
		"guard_name": "trim",
	}
}

func (r *StorePermissionRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"name":       "required|max_len:255",
		"guard_name": "required|max_len:255",
	}
}

func (r *StorePermissionRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		"guard_name.required": "请填写守卫名",
		"name.required":       "请填写权限名",
	}
}

func (r *StorePermissionRequest) Attributes(ctx http.Context) map[string]string {
	return map[string]string{}
}

func (r *StorePermissionRequest) PrepareForValidation(ctx http.Context, data validation.Data) error {
	return nil
}
