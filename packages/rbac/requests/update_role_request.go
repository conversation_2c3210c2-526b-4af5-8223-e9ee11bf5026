package requests

import (
	"github.com/goravel/framework/contracts/http"
)

type UpdateRoleRequest struct {
	ID        uint   `form:"id" json:"id"`
	Name      string `form:"name" json:"name"`
	GuardName string `form:"guard_name" json:"guard_name"`
}

func (r *UpdateRoleRequest) Authorize(ctx http.Context) error {
	return nil
}

func (r *UpdateRoleRequest) Filters(ctx http.Context) map[string]string {
	return map[string]string{
		"name":       "trim",
		"guard_name": "trim",
	}
}

func (r *UpdateRoleRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"id":         "required|numeric",
		"name":       "required|max_len:255",
		"guard_name": "required|max_len:255",
	}
}

func (r *UpdateRoleRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		"id.required":         "角色ID不能为空",
		"name.required":       "角色名称不能为空",
		"guard_name.required": "守卫名称不能为空",
	}
}

func (r *UpdateRoleRequest) Attributes(ctx http.Context) map[string]string {
	return map[string]string{
		"id":         "角色ID",
		"name":       "角色名称",
		"guard_name": "守卫名称",
	}
}
