package requests

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/contracts/validation"
)

type StoreRoleRequest struct {
	Name      string `form:"name" json:"name"`
	GuardName string `form:"guard_name" json:"guard_name"`
}

func (r *StoreRoleRequest) Authorize(ctx http.Context) error {
	return nil
}

func (r *StoreRoleRequest) Filters(ctx http.Context) map[string]string {
	return map[string]string{
		"name":       "trim",
		"guard_name": "trim",
	}
}

func (r *StoreRoleRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"name":       "required|max_len:255",
		"guard_name": "required|max_len:255",
	}
}

func (r *StoreRoleRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		"guard_name.required": "请填写守卫名",
		"name.required":       "请填写角色名",
	}
}

func (r *StoreRoleRequest) Attributes(ctx http.Context) map[string]string {
	return map[string]string{}
}

func (r *StoreRoleRequest) PrepareForValidation(ctx http.Context, data validation.Data) error {
	return nil
}
