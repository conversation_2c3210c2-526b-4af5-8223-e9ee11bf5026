package requests

import (
	"github.com/goravel/framework/contracts/http"
)

// StoreMenuRequest 创建菜单请求
type StoreMenuRequest struct {
	Name      string `form:"name" json:"name" binding:"required"`
	Route     string `form:"route" json:"route"`
	ParentID  uint   `form:"parent_id" json:"parent_id"`
	Order     int    `form:"order" json:"order"`
	Icon      string `form:"icon" json:"icon"`
	GuardName string `form:"guard_name" json:"guard_name" binding:"required"`
}

// Authorize 授权验证
func (r *StoreMenuRequest) Authorize(ctx http.Context) error {
	return nil
}

// Rules 验证规则
func (r *StoreMenuRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"name":       "required|max_len:255",
		"guard_name": "required|max_len:255",
		"route":      "required|max_len:255",
		"parent_id":  "numeric",
		"order":      "numeric",
		"icon":       "max_len:255",
	}
}

// Messages 错误信息
func (r *StoreMenuRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		"name.required":       "菜单名称不能为空",
		"name.max_len":        "菜单名称不能超过255个字符",
		"guard_name.required": "守卫名称不能为空",
		"guard_name.max_len":  "守卫名称不能超过255个字符",
		"route.required":      "菜单路由不能为空",
		"route.max_len":       "菜单路由不能超过255个字符",
		"parent_id.numeric":   "父级菜单ID必须是数字",
		"order.numeric":       "菜单排序必须是数字",
		"icon.max_len":        "菜单图标不能超过255个字符",
	}
}
