package rbac

import (
	"fmt"
	"strconv"

	"github.com/goravel/framework/contracts/http"
	facades2 "github.com/klphp/goravel/packages/core/response/facades"
	"github.com/klphp/goravel/packages/rbac/models"
)

// AssignPermissionToRoleController godoc
// @Summary 分配权限给角色
// @Description 将指定权限分配给角色
// @Tags RBAC-权限分配
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param permission_id formData int true "权限ID"
// @Param role_id formData int true "角色ID"
// @Success 200 {object} map[string]interface{} "分配成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "权限或角色不存在"
// @Router /admin/auth/assign-permission-to-role [post]
func (a *RBAC) AssignPermissionToRoleController(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取请求参数
	permissionID, err := strconv.Atoi(ctx.Request().Input("permission_id"))
	if err != nil || permissionID <= 0 {
		return resp.Error(fmt.Errorf("无效的权限ID"))
	}

	roleID, err := strconv.Atoi(ctx.Request().Input("role_id"))
	if err != nil || roleID <= 0 {
		return resp.Error(fmt.Errorf("无效的角色ID"))
	}

	// 检查权限是否存在
	_, err = a.permissionService.Get(uint(permissionID))
	if err != nil {
		return resp.Error(fmt.Errorf("权限不存在"))
	}

	// 检查角色是否存在
	_, err = a.roleService.Get(uint(roleID))
	if err != nil {
		return resp.Error(fmt.Errorf("角色不存在"))
	}

	// 分配权限给角色
	err = a.AssignPermissionToRole(uint(permissionID), uint(roleID))
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(nil)
}

// RemovePermissionFromRoleController godoc
// @Summary 从角色移除权限
// @Description 从指定角色移除权限
// @Tags RBAC-权限分配
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param permission_id formData int true "权限ID"
// @Param role_id formData int true "角色ID"
// @Success 200 {object} map[string]interface{} "移除成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "权限或角色不存在"
// @Router /admin/auth/remove-permission-from-role [post]
func (a *RBAC) RemovePermissionFromRoleController(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取请求参数
	permissionID, err := strconv.Atoi(ctx.Request().Input("permission_id"))
	if err != nil || permissionID <= 0 {
		return resp.Error(fmt.Errorf("无效的权限ID"))
	}

	roleID, err := strconv.Atoi(ctx.Request().Input("role_id"))
	if err != nil || roleID <= 0 {
		return resp.Error(fmt.Errorf("无效的角色ID"))
	}

	// 检查权限是否存在
	_, err = a.permissionService.Get(uint(permissionID))
	if err != nil {
		return resp.Error(fmt.Errorf("权限不存在"))
	}

	// 检查角色是否存在
	_, err = a.roleService.Get(uint(roleID))
	if err != nil {
		return resp.Error(fmt.Errorf("角色不存在"))
	}

	// 从角色中移除权限
	err = a.RemovePermissionFromRole(uint(permissionID), uint(roleID))
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(nil)
}

// AssignPermissionToUserController godoc
// @Summary 分配权限给用户
// @Description 将指定权限直接分配给用户
// @Tags RBAC-权限分配
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param permission_id formData int true "权限ID"
// @Param model_id formData int true "用户ID"
// @Param model_type formData string false "用户类型" default("App\\Models\\User")
// @Success 200 {object} map[string]interface{} "分配成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "权限或用户不存在"
// @Router /admin/auth/assign-permission-to-user [post]
func (a *RBAC) AssignPermissionToUserController(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取请求参数
	permissionID, err := strconv.Atoi(ctx.Request().Input("permission_id"))
	if err != nil || permissionID <= 0 {
		return resp.Error(fmt.Errorf("无效的权限ID"))
	}

	modelID, err := strconv.Atoi(ctx.Request().Input("model_id"))
	if err != nil || modelID <= 0 {
		return resp.Error(fmt.Errorf("无效的用户ID"))
	}

	modelType := ctx.Request().Input("model_type", "models.User")

	// 检查权限是否存在
	_, err = a.permissionService.Get(uint(permissionID))
	if err != nil {
		return resp.Error(fmt.Errorf("权限不存在"))
	}

	// 分配权限给用户
	err = a.AssignPermissionToUser(uint(permissionID), uint(modelID), modelType)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(nil)
}

// RemovePermissionFromUserController godoc
// @Summary 从用户移除权限
// @Description 从指定用户移除直接分配的权限
// @Tags RBAC-权限分配
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param permission_id formData int true "权限ID"
// @Param model_id formData int true "用户ID"
// @Param model_type formData string false "用户类型" default("App\\Models\\User")
// @Success 200 {object} map[string]interface{} "移除成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "权限或用户不存在"
// @Router /admin/auth/remove-permission-from-user [post]
func (a *RBAC) RemovePermissionFromUserController(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取请求参数
	permissionID, err := strconv.Atoi(ctx.Request().Input("permission_id"))
	if err != nil || permissionID <= 0 {
		return resp.Error(fmt.Errorf("无效的权限ID"))
	}

	modelID, err := strconv.Atoi(ctx.Request().Input("model_id"))
	if err != nil || modelID <= 0 {
		return resp.Error(fmt.Errorf("无效的用户ID"))
	}

	modelType := ctx.Request().Input("model_type", "models.User")

	// 检查权限是否存在
	_, err = a.permissionService.Get(uint(permissionID))
	if err != nil {
		return resp.Error(fmt.Errorf("权限不存在"))
	}

	// 从用户中移除权限
	err = a.RemovePermissionFromUser(uint(permissionID), uint(modelID), modelType)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(nil)
}

// GetUserPermissionsController godoc
// @Summary 获取用户权限
// @Description 获取指定用户拥有的所有权限，包括直接分配的权限和通过角色获得的权限
// @Tags RBAC-权限分配
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param model_id query int true "用户ID"
// @Param model_type query string false "用户类型" default("App\\Models\\User")
// @Param guard_name query string false "守卫名称" default(admin)
// @Success 200 {object} map[string]interface{} "用户权限列表"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/user-permissions [get]
func (a *RBAC) GetUserPermissionsController(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取请求参数
	modelID, err := strconv.Atoi(ctx.Request().Query("model_id", "0"))
	if err != nil || modelID <= 0 {
		return resp.Error(fmt.Errorf("无效的用户ID"))
	}

	modelType := ctx.Request().Query("model_type", "models.User")
	guardName := ctx.Request().Query("guard_name", "admin")

	// 获取用户直接拥有的权限
	var directPermissions []models.Permission
	err = resp.Query().Raw(`
		SELECT auth_permissions.* FROM auth_permissions
		JOIN auth_model_has_permissions ON auth_permissions.id = auth_model_has_permissions.permission_id
		WHERE auth_model_has_permissions.model_id = ?
		AND auth_model_has_permissions.model_type = ?
		AND auth_permissions.guard_name = ?
	`, modelID, modelType, guardName).Find(&directPermissions)

	if err != nil {
		return resp.Error(err)
	}

	// 获取用户通过角色拥有的权限
	var rolePermissions []models.Permission
	err = resp.Query().Raw(`
		SELECT auth_permissions.* FROM auth_permissions
		JOIN auth_role_has_permissions ON auth_permissions.id = auth_role_has_permissions.permission_id
		JOIN auth_model_has_roles ON auth_role_has_permissions.role_id = auth_model_has_roles.role_id
		WHERE auth_model_has_roles.model_id = ?
		AND auth_model_has_roles.model_type = ?
		AND auth_permissions.guard_name = ?
	`, modelID, modelType, guardName).Find(&rolePermissions)

	if err != nil {
		return resp.Error(err)
	}

	// 合并权限并去重
	permissionMap := make(map[uint]models.Permission)
	for _, p := range directPermissions {
		permissionMap[p.ID] = p
	}
	for _, p := range rolePermissions {
		permissionMap[p.ID] = p
	}

	allPermissions := make([]models.Permission, 0, len(permissionMap))
	for _, p := range permissionMap {
		allPermissions = append(allPermissions, p)
	}

	return resp.Success(map[string]interface{}{
		"direct_permissions": directPermissions,
		"role_permissions":   rolePermissions,
		"all_permissions":    allPermissions,
	})
}

// HasPermissionController godoc
// @Summary 检查用户是否拥有权限
// @Description 检查指定用户是否拥有指定权限
// @Tags RBAC-权限分配
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param model_id query int true "用户ID"
// @Param model_type query string false "用户类型" default("App\\Models\\User")
// @Param permission query string true "权限名称"
// @Param guard_name query string false "守卫名称" default(admin)
// @Success 200 {object} map[string]bool "检查结果"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/has-permission [get]
func (a *RBAC) HasPermissionController(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取请求参数
	modelID, err := strconv.Atoi(ctx.Request().Query("model_id", "0"))
	if err != nil || modelID <= 0 {
		return resp.Error(fmt.Errorf("无效的用户ID"))
	}

	modelType := ctx.Request().Query("model_type", "models.User")
	permissionName := ctx.Request().Query("permission", "")
	guardName := ctx.Request().Query("guard_name", "admin")

	if permissionName == "" {
		return resp.Error(fmt.Errorf("权限名称不能为空"))
	}

	// 检查用户是否拥有指定权限
	hasPermission, err := a.HasPermission(uint(modelID), modelType, permissionName, guardName)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(map[string]interface{}{
		"has_permission": hasPermission,
	})
}
