package rbac

import (
	"github.com/goravel/framework/contracts/foundation"
	"github.com/goravel/framework/contracts/route"
	"github.com/goravel/framework/facades"

	"github.com/klphp/goravel/packages/common/middleware"
)

const Binding = "rbac"

var App foundation.Application

type ServiceProvider struct {
}

func (receiver *ServiceProvider) Register(app foundation.Application) {
	App = app

	app.Bind(Binding, func(app foundation.Application) (any, error) {
		return NewRBAC(), nil
	})
}

func (receiver *ServiceProvider) Boot(app foundation.Application) {
	r := app.MakeRoute()
	prefix := facades.Config().GetString("http.route_prefix.admin")
	adminRouteAuth := r.Prefix(prefix).Middleware(middleware.AutoTransaction(), middleware.AuthSanctum("admin"))
	// 需要认证的路由
	adminRouteAuth.Group(func(g route.Router) {
		rbac := NewRBAC()

		// 角色管理路由
		g.Post("/auth/role", rbac.StoreRole)
		g.Get("/auth/roles", rbac.GetRoles)
		g.Get("/auth/role/{id}", rbac.ShowRole)
		g.Put("/auth/role/{id}", rbac.UpdateRole)
		g.Delete("/auth/role/{id}", rbac.DestroyRole)

		// 权限管理路由
		g.Post("/auth/permission", rbac.StorePermission)
		g.Get("/auth/permissions", rbac.GetPermissions)
		g.Get("/auth/permission/{id}", rbac.ShowPermission)
		g.Put("/auth/permission/{id}", rbac.UpdatePermission)
		g.Delete("/auth/permission/{id}", rbac.DestroyPermission)

		// 菜单管理路由
		g.Post("/auth/menu", rbac.StoreMenu)
		g.Get("/auth/menus", rbac.GetMenus)
		g.Get("/auth/menu/{id}", rbac.GetMenu)
		g.Put("/auth/menu/{id}", rbac.UpdateMenu)
		g.Delete("/auth/menu/{id}", rbac.DeleteMenu)
		g.Get("/auth/user-menus", rbac.GetUserMenus)

		// 角色分配路由
		g.Post("/auth/assign-role", rbac.AssignRoleToUserController)
		g.Post("/auth/remove-role", rbac.RemoveRoleFromUserController)
		g.Get("/auth/user-roles", rbac.GetUserRolesController)

		// 权限分配路由
		g.Post("/auth/assign-permission-to-role", rbac.AssignPermissionToRoleController)
		g.Post("/auth/remove-permission-from-role", rbac.RemovePermissionFromRoleController)
		g.Post("/auth/assign-permission-to-user", rbac.AssignPermissionToUserController)
		g.Post("/auth/remove-permission-from-user", rbac.RemovePermissionFromUserController)
		g.Get("/auth/user-permissions", rbac.GetUserPermissionsController)

		// 权限检查路由
		g.Get("/auth/has-role", rbac.HasRoleController)
		g.Get("/auth/has-permission", rbac.HasPermissionController)
	})
}
