package services

import (
	"github.com/goravel/framework/contracts/database/orm"
	query "github.com/goravel/framework/database/orm"
	"github.com/goravel/framework/facades"
	"github.com/klphp/goravel/packages/rbac/models"
)

// PermissionService 提供权限相关的服务
type PermissionService struct{}

// NewPermissionService 创建一个新的权限服务实例
func NewPermissionService() *PermissionService {
	return &PermissionService{}
}

// Create 创建一个新权限
func (s *PermissionService) Create(name string, guardName string) (*models.Permission, error) {
	permission := &models.Permission{
		Name:      name,
		GuardName: guardName,
	}

	err := facades.Orm().Query().Create(permission)
	return permission, err
}

// Get 获取权限
func (s *PermissionService) Get(id uint) (*models.Permission, error) {
	var permission models.Permission
	err := facades.Orm().Query().Find(&permission, id)
	return &permission, err
}

// Delete 删除权限
func (s *PermissionService) Delete(id uint) error {
	// 删除权限时，同时删除相关的角色-权限关联和模型-权限关联

	err := facades.Orm().Transaction(func(tx orm.Query) error {
		// 删除角色-权限关联
		_, err := tx.Where("permission_id = ?", id).Delete(&models.RoleHasPermission{})
		if err != nil {
			return err
		}

		// 删除模型-权限关联
		_, err = tx.Where("permission_id = ?", id).Delete(&models.ModelHasPermission{})
		if err != nil {
			return err
		}

		// 删除权限
		_, err = tx.Delete(&models.Permission{Model: query.Model{ID: id}})
		if err != nil {
			return err
		}

		return nil
	})

	return err
}

// AssignPermissionToRole 将权限分配给角色
func (s *PermissionService) AssignPermissionToRole(permissionID uint, roleID uint) error {
	roleHasPermission := &models.RoleHasPermission{
		PermissionID: permissionID,
		RoleID:       roleID,
	}

	// 检查是否已存在相同的记录
	var count int64
	err := facades.Orm().Query().Model(&models.RoleHasPermission{}).Where(
		"permission_id = ? AND role_id = ?",
		permissionID, roleID,
	).Count(&count)

	if err != nil {
		return err
	}

	if count > 0 {
		// 已存在，不需要重复创建
		return nil
	}

	return facades.Orm().Query().Create(roleHasPermission)
}

// RemovePermissionFromRole 从角色中移除权限
func (s *PermissionService) RemovePermissionFromRole(permissionID uint, roleID uint) error {
	_, err := facades.Orm().Query().Where(
		"permission_id = ? AND role_id = ?",
		permissionID, roleID,
	).Delete(&models.RoleHasPermission{})
	return err
}

// AssignPermissionToModel 直接将权限分配给模型（用户或其他模型）
func (s *PermissionService) AssignPermissionToModel(permissionID uint, modelID uint, modelType string) error {
	modelHasPermission := &models.ModelHasPermission{
		PermissionID: permissionID,
		ModelID:      modelID,
		ModelType:    modelType,
	}

	// 检查是否已存在相同的记录
	var count int64
	err := facades.Orm().Query().Model(&models.ModelHasPermission{}).Where(
		"permission_id = ? AND model_id = ? AND model_type = ?",
		permissionID, modelID, modelType,
	).Count(&count)

	if err != nil {
		return err
	}

	if count > 0 {
		// 已存在，不需要重复创建
		return nil
	}

	return facades.Orm().Query().Create(modelHasPermission)
}

// RemovePermissionFromModel 从模型中移除直接权限
func (s *PermissionService) RemovePermissionFromModel(permissionID uint, modelID uint, modelType string) error {
	_, err := facades.Orm().Query().Where(
		"permission_id = ? AND model_id = ? AND model_type = ?",
		permissionID, modelID, modelType,
	).Delete(&models.ModelHasPermission{})
	return err
}

// HasPermission 检查模型是否拥有指定权限
func (s *PermissionService) HasPermission(modelID uint, modelType string, permissionName string, guardName string) (bool, error) {
	// 查询权限ID
	var permission models.Permission
	err := facades.Orm().Query().Where("name = ? AND guard_name = ?", permissionName, guardName).First(&permission)
	if err != nil {
		return false, err
	}

	// 检查模型是否直接拥有该权限
	var directCount int64
	err = facades.Orm().Query().Model(&models.ModelHasPermission{}).Where(
		"permission_id = ? AND model_id = ? AND model_type = ?",
		permission.ID, modelID, modelType,
	).Count(&directCount)

	if err != nil {
		return false, err
	}

	if directCount > 0 {
		return true, nil
	}

	// 检查模型通过角色是否拥有该权限
	// 1. 获取模型拥有的所有角色ID
	var modelRoles []*models.ModelHasRole
	err = facades.Orm().Query().Where(
		"model_id = ? AND model_type = ?",
		modelID, modelType,
	).Find(&modelRoles)

	if err != nil {
		return false, err
	}

	if len(modelRoles) == 0 {
		return false, nil
	}

	// 提取角色ID
	roleIDs := make([]uint, len(modelRoles))
	for i, mr := range modelRoles {
		roleIDs[i] = mr.RoleID
	}

	// 2. 检查这些角色是否拥有该权限
	var rolePermCount int64
	// Convert roleIDs from []uint to []any
	roleIDsAny := make([]any, len(roleIDs))
	for i, id := range roleIDs {
		roleIDsAny[i] = id
	}
	err = facades.Orm().Query().Model(&models.RoleHasPermission{}).Where(
		"permission_id = ?", permission.ID,
	).WhereIn("role_id", roleIDsAny).Count(&rolePermCount)

	return rolePermCount > 0, err
}

// GetModelPermissions 获取模型拥有的所有权限（包括直接权限和通过角色获得的权限）
func (s *PermissionService) GetModelPermissions(modelID uint, modelType string) ([]*models.Permission, error) {
	var permissions []*models.Permission
	var permissionIDs []uint

	// 1. 获取直接分配给模型的权限ID
	var modelPermissions []*models.ModelHasPermission
	err := facades.Orm().Query().Where(
		"model_id = ? AND model_type = ?",
		modelID, modelType,
	).Find(&modelPermissions)

	if err != nil {
		return nil, err
	}

	for _, mp := range modelPermissions {
		permissionIDs = append(permissionIDs, mp.PermissionID)
	}

	// 2. 获取模型拥有的所有角色ID
	var modelRoles []*models.ModelHasRole
	err = facades.Orm().Query().Where(
		"model_id = ? AND model_type = ?",
		modelID, modelType,
	).Find(&modelRoles)

	if err != nil {
		return nil, err
	}

	if len(modelRoles) > 0 {
		// 提取角色ID
		roleIDs := make([]uint, len(modelRoles))
		for i, mr := range modelRoles {
			roleIDs[i] = mr.RoleID
		}

		// 3. 获取这些角色拥有的权限ID
		var rolePermissions []*models.RoleHasPermission
		// Convert roleIDs from []uint to []any
		roleIDsAny := make([]any, len(roleIDs))
		for i, id := range roleIDs {
			roleIDsAny[i] = id
		}
		err = facades.Orm().Query().WhereIn("role_id", roleIDsAny).Find(&rolePermissions)

		if err != nil {
			return nil, err
		}

		for _, rp := range rolePermissions {
			// 检查是否已存在，避免重复
			exists := false
			for _, pid := range permissionIDs {
				if pid == rp.PermissionID {
					exists = true
					break
				}
			}

			if !exists {
				permissionIDs = append(permissionIDs, rp.PermissionID)
			}
		}
	}

	if len(permissionIDs) == 0 {
		return permissions, nil
	}

	// 4. 查询权限详情
	// Convert permissionIDs from []uint to []any
	permissionIDsAny := make([]any, len(permissionIDs))
	for i, id := range permissionIDs {
		permissionIDsAny[i] = id
	}
	err = facades.Orm().Query().WhereIn("id", permissionIDsAny).Find(&permissions)
	return permissions, err
}
