package services

import (
	"github.com/goravel/framework/contracts/database/orm"
	query "github.com/goravel/framework/database/orm"
	"github.com/goravel/framework/facades"
	"github.com/klphp/goravel/packages/rbac/contracts"
	"github.com/klphp/goravel/packages/rbac/models"
)

// MenuService 提供菜单相关的服务
type MenuService struct{}

// NewMenuService 创建一个新的菜单服务实例
func NewMenuService() *MenuService {
	return &MenuService{}
}

// Create 创建一个新菜单
func (s *MenuService) Create(menu *models.Menu) (*models.Menu, error) {
	err := facades.Orm().Query().Create(menu)
	return menu, err
}

// Get 获取菜单
func (s *MenuService) Get(id uint) (*models.Menu, error) {
	var menu models.Menu
	err := facades.Orm().Query().Find(&menu, id)
	return &menu, err
}

// Delete 删除菜单
func (s *MenuService) Delete(id uint) error {
	// 删除菜单时，同时删除其子菜单
	err := facades.Orm().Transaction(func(tx orm.Query) error {
		// 删除子菜单
		_, err := tx.Where("parent_id = ?", id).Delete(&models.Menu{})
		if err != nil {
			return err
		}

		// 删除菜单
		_, err = tx.Delete(&models.Menu{Model: query.Model{ID: id}})
		if err != nil {
			return err
		}

		return nil
	})

	return err
}

// GetByGuard 获取指定守卫下的所有菜单
func (s *MenuService) GetByGuard(guardName string) ([]*models.Menu, error) {
	var menus []*models.Menu
	err := facades.Orm().Query().Where("guard_name = ?", guardName).Order("`order` asc").Find(&menus)
	return menus, err
}

// GetMenuTree 获取菜单树结构
func (s *MenuService) GetMenuTree(guardName string) ([]*contracts.MenuTree, error) {
	// 获取所有菜单
	menus, err := s.GetByGuard(guardName)
	if err != nil {
		return nil, err
	}

	// 构建菜单树
	return s.buildMenuTree(menus, nil), nil
}

// GetUserMenus 获取用户有权限访问的菜单
func (s *MenuService) GetUserMenus(modelID uint, modelType string, guardName string, permissionService *PermissionService) ([]*contracts.MenuTree, error) {
	// 获取用户所有权限
	permissions, err := permissionService.GetModelPermissions(modelID, modelType)
	if err != nil {
		return nil, err
	}

	// 获取所有菜单
	menus, err := s.GetByGuard(guardName)
	if err != nil {
		return nil, err
	}

	// 过滤出用户有权限访问的菜单
	var accessibleMenus []*models.Menu
	permissionMap := make(map[string]bool)

	// 构建权限映射
	for _, p := range permissions {
		permissionMap[p.Name] = true
	}

	// 过滤菜单
	for _, menu := range menus {
		// 如果菜单没有关联路由，则默认可访问
		if menu.Route == nil || *menu.Route == "" {
			accessibleMenus = append(accessibleMenus, menu)
			continue
		}

		// 检查用户是否有访问该菜单的权限
		// 假设菜单路由对应的权限名称为 "访问:{路由}"
		permissionName := "访问:" + *menu.Route
		if permissionMap[permissionName] {
			accessibleMenus = append(accessibleMenus, menu)
		}
	}

	// 构建菜单树
	return s.buildMenuTree(accessibleMenus, nil), nil
}

// buildMenuTree 构建菜单树结构
func (s *MenuService) buildMenuTree(menus []*models.Menu, parentID *uint) []*contracts.MenuTree {
	var result []*contracts.MenuTree

	for _, menu := range menus {
		// 检查是否为当前层级的菜单
		if (parentID == nil && menu.ParentID == nil) || (parentID != nil && menu.ParentID != nil && *parentID == *menu.ParentID) {
			// 创建菜单树节点
			node := &contracts.MenuTree{
				ID:    menu.ID,
				Name:  menu.Name,
				Route: menu.Route,
				Icon:  menu.Icon,
				Order: menu.Order,
			}

			// 递归获取子菜单
			node.Children = s.buildMenuTree(menus, &menu.ID)

			result = append(result, node)
		}
	}

	return result
}
