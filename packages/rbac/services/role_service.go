package services

import (
	"github.com/goravel/framework/contracts/database/orm"
	query "github.com/goravel/framework/database/orm"
	"github.com/goravel/framework/facades"
	"github.com/klphp/goravel/packages/rbac/models"
)

// RoleService 提供角色相关的服务
type RoleService struct{}

// NewRoleService 创建一个新的角色服务实例
func NewRoleService() *RoleService {
	return &RoleService{}
}

// Create 创建一个新角色
func (s *RoleService) Create(name string, guardName string) (*models.Role, error) {
	role := &models.Role{
		Name:      name,
		GuardName: guardName,
	}

	err := facades.Orm().Query().Create(role)
	return role, err
}

// Get 获取角色
func (s *RoleService) Get(id uint) (*models.Role, error) {
	var role models.Role
	err := facades.Orm().Query().Find(&role, id)
	return &role, err
}

// Delete 删除角色
func (s *RoleService) Delete(id uint) error {
	// 删除角色时，同时删除相关的角色-权限关联和模型-角色关联

	err := facades.Orm().Transaction(func(tx orm.Query) error {
		// 删除角色-权限关联
		_, err := tx.Where("role_id = ?", id).Delete(&models.RoleHasPermission{})
		if err != nil {
			return err
		}

		// 删除模型-角色关联
		_, err = tx.Where("role_id = ?", id).Delete(&models.ModelHasRole{})
		if err != nil {
			return err
		}

		// 删除角色

		_, err = tx.Delete(&models.Role{Model: query.Model{ID: id}})
		if err != nil {
			return err
		}

		return nil
	})

	return err
}

// AssignRoleToModel 将角色分配给模型（用户或其他模型）
func (s *RoleService) AssignRoleToModel(roleID uint, modelID uint, modelType string) error {
	modelHasRole := &models.ModelHasRole{
		RoleID:    roleID,
		ModelID:   modelID,
		ModelType: modelType,
	}

	// 检查是否已存在相同的记录
	var count int64
	err := facades.Orm().Query().Model(&models.ModelHasRole{}).Where(
		"role_id = ? AND model_id = ? AND model_type = ?",
		roleID, modelID, modelType,
	).Count(&count)

	if err != nil {
		return err
	}

	if count > 0 {
		// 已存在，不需要重复创建
		return nil
	}

	return facades.Orm().Query().Create(modelHasRole)
}

// RemoveRoleFromModel 从模型中移除角色
func (s *RoleService) RemoveRoleFromModel(roleID uint, modelID uint, modelType string) error {
	_, err := facades.Orm().Query().Where(
		"role_id = ? AND model_id = ? AND model_type = ?",
		roleID, modelID, modelType,
	).Delete(&models.ModelHasRole{})
	return err
}

// HasRole 检查模型是否拥有指定角色
func (s *RoleService) HasRole(modelID uint, modelType string, roleName string, guardName string) (bool, error) {
	// 查询角色ID
	var role models.Role
	err := facades.Orm().Query().Where("name = ? AND guard_name = ?", roleName, guardName).First(&role)
	if err != nil {
		return false, err
	}

	// 检查模型是否拥有该角色
	var count int64
	err = facades.Orm().Query().Model(&models.ModelHasRole{}).Where(
		"role_id = ? AND model_id = ? AND model_type = ?",
		role.ID, modelID, modelType,
	).Count(&count)

	return count > 0, err
}

// GetModelRoles 获取模型拥有的所有角色
func (s *RoleService) GetModelRoles(modelID uint, modelType string) ([]*models.Role, error) {
	var roles []*models.Role

	// 查询模型拥有的角色ID
	var modelRoles []*models.ModelHasRole
	err := facades.Orm().Query().Where(
		"model_id = ? AND model_type = ?",
		modelID, modelType,
	).Find(&modelRoles)

	if err != nil {
		return nil, err
	}

	if len(modelRoles) == 0 {
		return roles, nil
	}

	// 提取角色ID
	roleIDs := make([]uint, len(modelRoles))
	for i, mr := range modelRoles {
		roleIDs[i] = mr.RoleID
	}

	// 查询角色详情
	// Convert roleIDs to a slice of any
	roleIDsAny := make([]any, len(roleIDs))
	for i, id := range roleIDs {
		roleIDsAny[i] = id
	}

	//判断roleIDsAny是否为空，若为空则直接返回空切片
	if len(roleIDsAny) == 0 {
		return roles, nil
	}
	err = facades.Orm().Query().WhereIn("id", roleIDsAny).Find(&roles)
	return roles, err
}
