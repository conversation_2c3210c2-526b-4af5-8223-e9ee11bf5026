package rbac

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	facades2 "github.com/klphp/goravel/packages/core/response/facades"
	"github.com/klphp/goravel/packages/rbac/models"
	"github.com/klphp/goravel/packages/rbac/requests"
)

// StorePermission godoc
// @Summary 创建权限
// @Description 创建一个新的权限
// @Tags RBAC-权限管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param permission body requests.StorePermissionRequest true "权限信息"
// @Success 200 {object} SwaggerPermission "创建成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/permission [post]
func (a *RBAC) StorePermission(ctx http.Context) http.Response {

	resp := facades2.Response().Context(ctx)

	var storePermission requests.StorePermissionRequest
	errors, _ := ctx.Request().ValidateRequest(&storePermission)
	// 绑定参数
	if errors != nil {
		return resp.Error(fmt.Errorf("参数错误: %s", errors.One()))
	}
	permission := &models.Permission{
		Name:      storePermission.Name,
		GuardName: storePermission.GuardName,
	}

	// 验证权限是否存在
	var count int64
	err := resp.Query().Model(permission).Where(permission).Count(&count)
	if err != nil {
		return resp.Error(err)
	}
	if count > 0 {
		return resp.Error(fmt.Errorf("权限已存在"))
	}

	// 创建权限
	permission, err = a.permissionService.Create(permission.Name, permission.GuardName)
	if err != nil {
		return resp.Error(err)
	}
	return resp.Success(permission)
}

// GetPermissions godoc
// @Summary 获取权限列表
// @Description 获取所有权限的列表，支持分页和筛选
// @Tags RBAC-权限管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param page query int false "页码" default(1)
// @Param per_page query int false "每页数量" default(15)
// @Param name query string false "权限名称（模糊搜索）"
// @Param guard_name query string false "守卫名称"
// @Success 200 {object} map[string]interface{} "权限列表"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/permissions [get]
func (a *RBAC) GetPermissions(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取分页参数
	page := ctx.Request().QueryInt("page", 1)
	perPage := ctx.Request().QueryInt("per_page", 15)

	// 查询条件
	name := ctx.Request().Query("name", "")
	guardName := ctx.Request().Query("guard_name", "")

	// 构建查询
	query := resp.Query().Model(&models.Permission{})

	// 添加过滤条件
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if guardName != "" {
		query = query.Where("guard_name = ?", guardName)
	}

	// 执行分页查询
	var permissions []models.Permission
	var total int64
	err := query.Paginate(page, perPage, &permissions, &total)
	if err != nil {
		return resp.Error(err)
	}

	// 构建分页响应数据
	paginator := map[string]interface{}{
		"data":  permissions,
		"total": total,
		"page":  page,
		"limit": perPage,
	}

	return resp.Success(paginator)
}

// ShowPermission godoc
// @Summary 获取权限详情
// @Description 获取指定权限的详细信息，包括拥有此权限的角色
// @Tags RBAC-权限管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param id path int true "权限ID"
// @Success 200 {object} map[string]interface{} "权限详情"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "权限不存在"
// @Router /admin/auth/permission/{id} [get]
func (a *RBAC) ShowPermission(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取权限ID
	id := ctx.Request().RouteInt("id")
	if id <= 0 {
		return resp.Error(fmt.Errorf("无效的权限ID"))
	}

	// 获取权限
	permission, err := a.permissionService.Get(uint(id))
	if err != nil {
		return resp.Error(err)
	}

	// 获取拥有此权限的角色
	var roles []models.Role
	err = resp.Query().Raw(`
		SELECT auth_roles.* FROM auth_roles
		JOIN auth_role_has_permissions ON auth_roles.id = auth_role_has_permissions.role_id
		WHERE auth_role_has_permissions.permission_id = ?
	`, id).Find(&roles)
	if err != nil {
		return resp.Error(err)
	}

	// 构建响应数据
	responseData := map[string]interface{}{
		"permission": permission,
		"roles":      roles,
	}

	return resp.Success(responseData)
}

// UpdatePermission godoc
// @Summary 更新权限
// @Description 更新指定权限的信息
// @Tags RBAC-权限管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param id path int true "权限ID"
// @Param permission body requests.UpdatePermissionRequest true "权限信息"
// @Success 200 {object} SwaggerPermission "更新成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "权限不存在"
// @Router /admin/auth/permission/{id} [put]
func (a *RBAC) UpdatePermission(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	var updatePermission requests.UpdatePermissionRequest
	errors, _ := ctx.Request().ValidateRequest(&updatePermission)
	// 绑定参数
	if errors != nil {
		return resp.Error(fmt.Errorf("参数错误: %s", errors.One()))
	}

	// 检查权限是否存在
	permission, err := a.permissionService.Get(updatePermission.ID)
	if err != nil {
		return resp.Error(err)
	}

	// 检查名称是否已被其他权限使用
	var count int64
	err = resp.Query().Model(&models.Permission{}).Where(
		"name = ? AND guard_name = ? AND id != ?",
		updatePermission.Name, updatePermission.GuardName, updatePermission.ID,
	).Count(&count)
	if err != nil {
		return resp.Error(err)
	}
	if count > 0 {
		return resp.Error(fmt.Errorf("权限名称已被使用"))
	}

	// 更新权限
	permission.Name = updatePermission.Name
	permission.GuardName = updatePermission.GuardName

	err = resp.Query().Save(permission)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(permission)
}

// DestroyPermission godoc
// @Summary 删除权限
// @Description 删除指定的权限
// @Tags RBAC-权限管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param id path int true "权限ID"
// @Success 200 {object} map[string]interface{} "删除成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "权限不存在"
// @Router /admin/auth/permission/{id} [delete]
func (a *RBAC) DestroyPermission(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取权限ID
	id := ctx.Request().RouteInt("id")
	if id <= 0 {
		return resp.Error(fmt.Errorf("无效的权限ID"))
	}

	// 删除权限
	err := a.permissionService.Delete(uint(id))
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(nil)
}
