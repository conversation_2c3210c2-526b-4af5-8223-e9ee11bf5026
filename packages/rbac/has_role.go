package rbac

import (
	"fmt"
	"strconv"

	"github.com/goravel/framework/contracts/http"
	facades2 "github.com/klphp/goravel/packages/core/response/facades"
	"github.com/klphp/goravel/packages/rbac/models"
)

// AssignRoleToUserController godoc
// @Summary 分配角色给用户
// @Description 将指定角色分配给用户
// @Tags RBAC-角色分配
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param role_id formData int true "角色ID"
// @Param model_id formData int true "用户ID"
// @Param model_type formData string false "用户类型" default("App\\Models\\User")
// @Success 200 {object} map[string]interface{} "分配成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "角色或用户不存在"
// @Router /admin/auth/assign-role [post]
func (a *RBAC) AssignRoleToUserController(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取请求参数
	roleID, err := strconv.Atoi(ctx.Request().Input("role_id"))
	if err != nil || roleID <= 0 {
		return resp.Error(fmt.Errorf("无效的角色ID"))
	}

	modelID, err := strconv.Atoi(ctx.Request().Input("model_id"))
	if err != nil || modelID <= 0 {
		return resp.Error(fmt.Errorf("无效的用户ID"))
	}

	modelType := ctx.Request().Input("model_type", "models.User")

	// 检查角色是否存在
	_, err = a.roleService.Get(uint(roleID))
	if err != nil {
		return resp.Error(fmt.Errorf("角色不存在"))
	}

	// 分配角色给用户
	err = a.AssignRoleToUser(uint(roleID), uint(modelID), modelType)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(nil)
}

// RemoveRoleFromUserController godoc
// @Summary 从用户移除角色
// @Description 从指定用户移除角色
// @Tags RBAC-角色分配
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param role_id formData int true "角色ID"
// @Param model_id formData int true "用户ID"
// @Param model_type formData string false "用户类型" default("App\\Models\\User")
// @Success 200 {object} map[string]interface{} "移除成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "角色或用户不存在"
// @Router /admin/auth/remove-role [post]
func (a *RBAC) RemoveRoleFromUserController(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取请求参数
	roleID, err := strconv.Atoi(ctx.Request().Input("role_id"))
	if err != nil || roleID <= 0 {
		return resp.Error(fmt.Errorf("无效的角色ID"))
	}

	modelID, err := strconv.Atoi(ctx.Request().Input("model_id"))
	if err != nil || modelID <= 0 {
		return resp.Error(fmt.Errorf("无效的用户ID"))
	}

	modelType := ctx.Request().Input("model_type", "models.User")

	// 检查角色是否存在
	_, err = a.roleService.Get(uint(roleID))
	if err != nil {
		return resp.Error(fmt.Errorf("角色不存在"))
	}

	// 从用户中移除角色
	err = a.RemoveRoleFromUser(uint(roleID), uint(modelID), modelType)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(nil)
}

// GetUserRolesController godoc
// @Summary 获取用户角色
// @Description 获取指定用户拥有的所有角色
// @Tags RBAC-角色分配
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param model_id query int true "用户ID"
// @Param model_type query string false "用户类型" default("App\\Models\\Admin")
// @Param guard_name query string false "守卫名称" default(admin)
// @Success 200 {array} SwaggerRole "用户角色列表"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/user-roles [get]
func (a *RBAC) GetUserRolesController(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取请求参数
	modelID, err := strconv.Atoi(ctx.Request().Query("model_id", "0"))
	if err != nil || modelID <= 0 {
		return resp.Error(fmt.Errorf("无效的用户ID"))
	}

	modelType := ctx.Request().Query("model_type", "models.Admin")
	guardName := ctx.Request().Query("guard_name", "admin")

	// 获取用户的角色
	var roles []models.Role
	err = resp.Query().Raw(`
		SELECT auth_roles.* FROM auth_roles
		JOIN auth_model_has_roles ON auth_roles.id = auth_model_has_roles.role_id
		WHERE auth_model_has_roles.model_id = ?
		AND auth_model_has_roles.model_type = ?
		AND auth_roles.guard_name = ?
	`, modelID, modelType, guardName).Find(&roles)

	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(roles)
}

// HasRoleController godoc
// @Summary 检查用户是否拥有角色
// @Description 检查指定用户是否拥有指定角色
// @Tags RBAC-角色分配
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer 用户令牌"
// @Param model_id query int true "用户ID"
// @Param model_type query string false "用户类型" default("App\\Models\\Admin")
// @Param role query string true "角色名称"
// @Param guard_name query string false "守卫名称" default(admin)
// @Success 200 {object} map[string]bool "检查结果"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Router /admin/auth/has-role [get]
func (a *RBAC) HasRoleController(ctx http.Context) http.Response {
	resp := facades2.Response().Context(ctx)

	// 获取请求参数
	modelID, err := strconv.Atoi(ctx.Request().Query("model_id", "0"))
	if err != nil || modelID <= 0 {
		return resp.Error(fmt.Errorf("无效的用户ID"))
	}

	modelType := ctx.Request().Query("model_type", "models.Admin")
	roleName := ctx.Request().Query("role", "")
	guardName := ctx.Request().Query("guard_name", "admin")

	if roleName == "" {
		return resp.Error(fmt.Errorf("角色名称不能为空"))
	}

	// 检查用户是否拥有指定角色
	hasRole, err := a.HasRole(uint(modelID), modelType, roleName, guardName)
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(map[string]interface{}{
		"has_role": hasRole,
	})
}
