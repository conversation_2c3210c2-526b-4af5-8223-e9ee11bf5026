package rbac

import (
	"encoding/json"

	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	"github.com/klphp/goravel/packages/rbac/contracts"
	"github.com/klphp/goravel/packages/rbac/models"
	"github.com/klphp/goravel/packages/rbac/services"
)

// RBAC 是RBAC权限管理系统的主要入口
type RBAC struct {
	roleService       *services.RoleService
	permissionService *services.PermissionService
	menuService       *services.MenuService
}

// NewAuth 创建一个新的Auth实例
func NewRBAC() *RBAC {
	return &RBAC{
		roleService:       services.NewRoleService(),
		permissionService: services.NewPermissionService(),
		menuService:       services.NewMenuService(),
	}
}

// CreateRole 创建一个新角色
func (a *RBAC) CreateRole(name string, guardName string) (*models.Role, error) {
	return a.roleService.Create(name, guardName)
}

// GetRoleByID 获取角色
func (a *RBAC) GetRoleByID(id uint) (*models.Role, error) {
	return a.roleService.Get(id)
}

// DeleteRoleByID 删除角色
func (a *RBAC) DeleteRoleByID(id uint) error {
	return a.roleService.Delete(id)
}

// CreatePermission 创建一个新权限
func (a *RBAC) CreatePermission(name string, guardName string) (*models.Permission, error) {
	return a.permissionService.Create(name, guardName)
}

// GetPermissionByID 获取权限
func (a *RBAC) GetPermissionByID(id uint) (*models.Permission, error) {
	return a.permissionService.Get(id)
}

// DeletePermissionByID 删除权限
func (a *RBAC) DeletePermissionByID(id uint) error {
	return a.permissionService.Delete(id)
}

// AssignRoleToUser 将角色分配给用户
func (a *RBAC) AssignRoleToUser(roleID uint, modelID uint, modelType string) error {
	return a.roleService.AssignRoleToModel(roleID, modelID, modelType)
}

// RemoveRoleFromUser 从用户中移除角色
func (a *RBAC) RemoveRoleFromUser(roleID uint, modelID uint, modelType string) error {
	return a.roleService.RemoveRoleFromModel(roleID, modelID, modelType)
}

// AssignPermissionToRole 将权限分配给角色
func (a *RBAC) AssignPermissionToRole(permissionID uint, roleID uint) error {
	return a.permissionService.AssignPermissionToRole(permissionID, roleID)
}

// RemovePermissionFromRole 从角色中移除权限
func (a *RBAC) RemovePermissionFromRole(permissionID uint, roleID uint) error {
	return a.permissionService.RemovePermissionFromRole(permissionID, roleID)
}

// AssignPermissionToUser 直接将权限分配给用户
func (a *RBAC) AssignPermissionToUser(permissionID uint, modelID uint, modelType string) error {
	return a.permissionService.AssignPermissionToModel(permissionID, modelID, modelType)
}

// RemovePermissionFromUser 从用户中移除直接权限
func (a *RBAC) RemovePermissionFromUser(permissionID uint, modelID uint, modelType string) error {
	return a.permissionService.RemovePermissionFromModel(permissionID, modelID, modelType)
}

// HasRole 检查用户是否拥有指定角色
func (a *RBAC) HasRole(modelID uint, modelType string, roleName string, guardName string) (bool, error) {
	return a.roleService.HasRole(modelID, modelType, roleName, guardName)
}

// HasPermission 检查用户是否拥有指定权限
func (a *RBAC) HasPermission(modelID uint, modelType string, permissionName string, guardName string) (bool, error) {
	return a.permissionService.HasPermission(modelID, modelType, permissionName, guardName)
}

// CreateMenu 创建菜单
func (a *RBAC) CreateMenu(menu *models.Menu) (*models.Menu, error) {
	return a.menuService.Create(menu)
}

// GetMenuByID 获取菜单
func (a *RBAC) GetMenuByID(id uint) (*models.Menu, error) {
	return a.menuService.Get(id)
}

// DeleteMenuByID 删除菜单
func (a *RBAC) DeleteMenuByID(id uint) error {
	return a.menuService.Delete(id)

}

// GetMenusByGuard 获取指定守卫下的所有菜单
func (a *RBAC) GetMenusByGuard(guardName string) ([]*models.Menu, error) {
	return a.menuService.GetByGuard(guardName)
}

// GetMenuTree 获取菜单树结构
func (a *RBAC) GetMenuTree(guardName string) ([]*contracts.MenuTree, error) {
	return a.menuService.GetMenuTree(guardName)
}

// GetUserMenusByModel 获取用户有权限访问的菜单
func (a *RBAC) GetUserMenusByModel(modelID uint, modelType string, guardName string) ([]*contracts.MenuTree, error) {
	return a.menuService.GetUserMenus(modelID, modelType, guardName, a.permissionService)
}

// Middleware 返回权限中间件
func (a *RBAC) Middleware(permission string, guardName string) http.Middleware {
	return func(ctx http.Context) {
		user := ctx.Request().Input("user")
		if user == "" {
			ctx.Response().Status(403).Json(http.Json{"message": "未授权访问"})
			return
		}

		// 获取用户ID和类型
		var userMap map[string]interface{}
		if err := json.Unmarshal([]byte(user), &userMap); err != nil {
			ctx.Response().Status(403).Json(http.Json{"message": "用户数据格式错误"})
			return
		}
		modelIDFloat, ok := userMap["id"].(float64)
		if !ok {
			ctx.Response().Status(403).Json(http.Json{"message": "用户ID格式错误"})
			return
		}
		modelID := uint(modelIDFloat)
		if !ok {
			ctx.Response().Status(403).Json(http.Json{"message": "用户ID格式错误"})
			return
		}

		modelType := userMap["model_type"].(string)
		if modelType == "" {
			modelType = "App\\Models\\User"
		}

		// 检查权限
		hasPermission, err := a.HasPermission(modelID, modelType, permission, guardName)
		if err != nil {
			facades.Log().Error(err)
			ctx.Response().Status(500).Json(http.Json{"message": "权限检查失败"})
			return
		}

		if !hasPermission {
			ctx.Response().Status(403).Json(http.Json{"message": "没有权限执行此操作"})
			return
		}

		ctx.Request().Next()
	}
}
