package models

import (
	"github.com/goravel/framework/database/orm"
	"github.com/goravel/framework/support/carbon"
)

type AdminsAuthTokens struct {
	orm.Model
	AdminId      uint            `json:"admin_id" gorm:"index"`
	TokenType    string          `json:"token_type" gorm:"size:36"`
	Token        string          `json:"token" gorm:"size:255"`
	RefreshToken string          `json:"refresh_token" gorm:"size:255;default:null"`
	ExpiresAt    carbon.DateTime `json:"expires_at"`
	LastUsedAt   carbon.DateTime `json:"last_used_at"`
	Abilities    string          `json:"abilities" gorm:"size:255;default:null"`
	Admin        Admin           `gorm:"foreignKey:AdminId"`
}

// TableName 指定表名
func (a *AdminsAuthTokens) TableName() string {
	return "admins_auth_tokens"
}
