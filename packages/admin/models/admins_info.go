package models

import (
	"github.com/goravel/framework/database/orm"
	"github.com/goravel/framework/support/carbon"
)

type AdminsInfo struct {
	orm.Model
	AdminId     uint            `json:"admin_id" gorm:"index"`
	RealName    string          `json:"real_name" gorm:"size:36;default:null"`
	Position    string          `json:"position" gorm:"size:50;default:null"`
	Department  string          `json:"department" gorm:"size:50;default:null"`
	EmployeeId  string          `json:"employee_id" gorm:"size:36;default:null"`
	HireDate    carbon.DateTime `json:"hire_date"`
	Permissions string          `json:"permissions" gorm:"type:text;default:null"`
	Settings    string          `json:"settings" gorm:"type:text;default:null"`
	Admin       Admin           `gorm:"foreignKey:AdminId"`
}

// TableName 指定表名
func (a *AdminsInfo) TableName() string {
	return "admins_info"
}
