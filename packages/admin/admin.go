package admin

import (
	"fmt"

	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	"github.com/klphp/gohelper/kl_array"
	"github.com/klphp/goravel/app/utils"
	"github.com/klphp/goravel/packages/admin/models"
	"github.com/klphp/goravel/packages/admin/requests"
	"github.com/klphp/goravel/packages/common"
	facades1 "github.com/klphp/goravel/packages/core/hashid/facades"
	facades3 "github.com/klphp/goravel/packages/core/response/facades"
	sf "github.com/klphp/goravel/packages/core/sanctum/facades"
)

type Admin struct {
}

func NewAdmin() *Admin {
	return &Admin{}
}

func (a *Admin) Login(ctx http.Context) http.Response {
	resp := facades3.Response().Context(ctx)

	var formData requests.SiteLoginRequest
	var err error
	errors, err := ctx.Request().ValidateRequest(&formData)
	if err != nil {
		return resp.Error(err)
	}
	if errors != nil {
		return resp.Error(fmt.Errorf("%s", errors.One()))
	}

	//帐号查询
	admin := &models.Admin{}
	err = facades.Orm().Query().Where("username = ? OR phone = ? OR email = ?", formData.Account, formData.Account, formData.Account).First(admin)
	if err != nil || admin.ID == 0 {
		return resp.Error(fmt.Errorf("帐号不存在或密码不匹配"))
	}

	if facades.Hash().Check(formData.Password, admin.Password) {
		//生成token，默认赋予所有权限
		Token := sf.Sanctum().Context(ctx)
		accessToken, err := Token.Generate(admin, utils.GUARD_ADMIN, []string{"*"})
		if err != nil {
			return resp.Error(err)
		}
		return resp.Success(accessToken)
	} else {
		return resp.Error(fmt.Errorf("帐号不正确或密码不匹配"))
	}
}

func (a *Admin) Register(ctx http.Context) http.Response {
	resp := facades3.Response().Context(ctx)

	// 获取请求参数，并在封装的请求中实现请求验证
	var formData requests.SiteSignupRequest
	var err error
	errors, err := ctx.Request().ValidateRequest(&formData)
	if err != nil {
		return resp.Error(err)
	}
	if errors != nil {
		return resp.Error(fmt.Errorf("%s", errors.One()))
	}

	// json绑定model
	admin := &models.Admin{}
	admin, err = kl_array.JsonBind(formData, admin)
	if err != nil {
		return resp.Error(err)
	}

	// 加密
	admin.Password, _ = facades.Hash().Make(admin.Password)
	// StringID
	admin.StrId, _ = facades1.HashId().CreateHashId()
	// Create admin
	if err = facades.Orm().Query().Create(admin); err != nil {
		return resp.Error(err)
	}

	Token := sf.Sanctum().Context(ctx)
	accessToken, err := Token.Generate(admin, utils.GUARD_ADMIN, []string{"*"})
	if err != nil {
		return resp.Error(err)
	}

	return resp.Success(accessToken)
}

// GetAdminByID 通过ID获取管理员
func (a *Admin) GetAdminByID(id uint) (*models.Admin, error) {
	admin := &models.Admin{}
	err := facades.Orm().Query().Where("id = ?", id).First(admin)
	if common.GormNotFound(err) {
		return nil, fmt.Errorf("用户不存在")
	}
	return admin, nil
}

// GetAdminByStrID 通过字符串ID获取管理员
func (a *Admin) GetAdminByStrID(strID string) (*models.Admin, error) {
	admin := &models.Admin{}
	err := facades.Orm().Query().Where("str_id = ?", strID).First(admin)
	if common.GormNotFound(err) {
		return nil, fmt.Errorf("用户不存在")
	}
	return admin, nil
}

// GetAdminByAccount 通过账号获取管理员
func (a *Admin) GetAdminByAccount(account string) (*models.Admin, error) {
	admin := &models.Admin{}
	err := facades.Orm().Query().Where("username = ? OR phone = ? OR email = ?", account, account, account).First(admin)
	if err != nil {
		return nil, err
	}
	return admin, nil
}

// UpdateAdmin 更新管理员信息
func (a *Admin) UpdateAdmin(admin *models.Admin) error {
	return facades.Orm().Query().Save(admin)
}

// CreateAdmin 创建管理员
func (a *Admin) CreateAdmin(admin *models.Admin) error {
	// 加密密码
	if admin.Password != "" {
		hashedPassword, err := facades.Hash().Make(admin.Password)
		if err != nil {
			return err
		}
		admin.Password = hashedPassword
	}

	return facades.Orm().Query().Create(admin)
}

// DeleteAdmin 删除管理员
func (a *Admin) DeleteAdmin(id uint) error {
	if _, err := facades.Orm().Query().Delete(&models.Admin{}, id); err != nil {
		return err
	}
	return nil
}

// VerifyPassword 验证管理员密码
func (a *Admin) VerifyPassword(admin *models.Admin, password string) bool {
	return facades.Hash().Check(password, admin.Password)
}

// UpdateAdminStatus 更新管理员状态
func (a *Admin) UpdateAdminStatus(admin *models.Admin, status int) error {
	admin.Status = int64(status)
	return facades.Orm().Query().Save(admin)
}

// UpdateAdminPassword 更新管理员密码
func (a *Admin) UpdateAdminPassword(admin *models.Admin, oldPassword string, newPassword string) error {
	// 验证旧密码是否正确
	if !a.VerifyPassword(admin, oldPassword) {
		return fmt.Errorf("旧密码不正确")
	}

	// 加密新密码
	hashedPassword, err := facades.Hash().Make(newPassword)
	if err != nil {
		return err
	}
	admin.Password = hashedPassword
	return facades.Orm().Query().Save(admin)
}

// GetAdminList 获取管理员列表
func (a *Admin) GetAdminList(page int, perPage int) ([]*models.Admin, int64, error) {
	var admins []*models.Admin
	var total int64

	query := facades.Orm().Query()

	// 获取总数
	err := query.Model(&models.Admin{}).Count(&total)
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	err = query.Offset((page - 1) * perPage).Limit(perPage).Find(&admins)
	if err != nil {
		return nil, 0, err
	}

	return admins, total, nil
}

// GetAdminByEmail 通过邮箱获取管理员
func (a *Admin) GetAdminByEmail(email string) (*models.Admin, error) {
	admin := &models.Admin{}
	err := facades.Orm().Query().Where("email = ?", email).First(admin)
	if err != nil {
		return nil, err
	}
	return admin, nil
}
