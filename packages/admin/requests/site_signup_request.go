package requests

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/contracts/validation"
)

type SiteSignupRequest struct {
	//StrId    *string `form:"str_id" json:"str_id"`
	Username             *string `form:"username" json:"username"`
	Email                *string `json:"email"`
	Phone                *string `json:"phone"`
	Password             *string `form:"password" json:"password"`
	PasswordConfirmation *string `form:"password_confirmation" json:"password_confirmation"`
}

func (r *SiteSignupRequest) Authorize(ctx http.Context) error {
	return nil
}

func (r *SiteSignupRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"username":              "required_without_all:phone,email|username|min_len:3|max_len:20|not_exists:admins,username",
		"phone":                 "required_without_all:username,email|mobile|not_exists:admins,phone",
		"email":                 "required_without_all:phone,username|email|not_exists:admins,email",
		"password":              "required|min_len:8",
		"password_confirmation": "required|eq_field:password",
	}
}

func (r *SiteSignupRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		"username.required_without_all":  "用户名、手机、email必填一项",
		"phone.required_without_all":     "用户名、手机、email必填一项",
		"email.required_without_all":     "用户名、手机、email必填一项",
		"username.username":              "用户名不能为纯数字或包含@字符",
		"password.required":              "密码不能为空",
		"phone.mobile":                   "手机号格式不正确",
		"email.email":                    "email格式不正确",
		"email.not_exists":               "帐号已存在",
		"username.not_exists":            "帐号已存在",
		"phone.not_exists":               "帐号已存在",
		"username.min_len":               "用户名不能少于3位",
		"username.max_len":               "用户名不能大于20位",
		"password.min_len":               "密码不能少于8位",
		"password_confirmation.required": "确认密码不能为空",
		"password_confirmation.eq_field": "密码不一致",
	}
}

func (r *SiteSignupRequest) Attributes(ctx http.Context) map[string]string {
	return map[string]string{}
}

func (r *SiteSignupRequest) PrepareForValidation(ctx http.Context, data validation.Data) error {
	return nil
}
