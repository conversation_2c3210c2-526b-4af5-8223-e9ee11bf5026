package requests

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/contracts/validation"
)

type SiteLoginRequest struct {
	Account  string `json:"account"`
	Password string `json:"password"`
}

func (r *SiteLoginRequest) Authorize(ctx http.Context) error {
	return nil
}

func (r *SiteLoginRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		`account`:  `required`,
		`password`: `required`,
	}
}

func (r *SiteLoginRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{
		`account.required`:  `帐号不能为空`,
		`password.required`: `密码不能为空`,
	}
}

func (r *SiteLoginRequest) Attributes(ctx http.Context) map[string]string {
	return map[string]string{}
}

func (r *SiteLoginRequest) PrepareForValidation(ctx http.Context, data validation.Data) error {
	return nil
}
