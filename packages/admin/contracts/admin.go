package contracts

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/klphp/goravel/packages/admin/models"
)

type Admin interface {
	// Login 管理员登录
	Login(ctx http.Context) http.Response

	// Register 管理员注册
	Register(ctx http.Context) http.Response

	// GetAdminByID 通过ID获取管理员
	GetAdminByID(id uint) (*models.Admin, error)

	// GetAdminByStrID 通过字符串ID获取管理员
	GetAdminByStrID(strID string) (*models.Admin, error)

	// GetAdminByAccount 通过账号获取管理员
	GetAdminByAccount(account string) (*models.Admin, error)

	// UpdateAdmin 更新管理员信息
	UpdateAdmin(admin *models.Admin) error

	// CreateAdmin 创建管理员
	CreateAdmin(admin *models.Admin) error

	// DeleteAdmin 删除管理员
	DeleteAdmin(id uint) error

	// VerifyPassword 验证管理员密码
	VerifyPassword(admin *models.Admin, password string) bool

	// UpdateAdminStatus 更新管理员状态
	UpdateAdminStatus(admin *models.Admin, status int) error

	// UpdateAdminPassword 更新管理员密码
	UpdateAdminPassword(admin *models.Admin, oldPassword string, newPassword string) error

	// GetAdminList 获取管理员列表
	GetAdminList(page int, perPage int) ([]*models.Admin, int64, error)

	// GetAdminByEmail 通过邮箱获取管理员
	GetAdminByEmail(email string) (*models.Admin, error)
}
