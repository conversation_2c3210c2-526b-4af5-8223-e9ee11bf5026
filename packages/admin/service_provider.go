package admin

import (
	"github.com/goravel/framework/contracts/foundation"
)

const Binding = "admin"

var App foundation.Application

type ServiceProvider struct {
}

func (receiver *ServiceProvider) Register(app foundation.Application) {
	App = app

	app.Bind(Binding, func(app foundation.Application) (any, error) {
		return NewAdmin(), nil
	})
}

func (receiver *ServiceProvider) Boot(app foundation.Application) {
	//route := app.MakeRoute()
	//admin := NewAdmin()
	//route.Get("/admin/login", admin.Login)
	//route.Get("/admin/register", admin.Register)
}
