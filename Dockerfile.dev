FROM golang:1.23.2-alpine

ENV GO111MODULE=on \
    CGO_ENABLED=0  \
    GOARCH="amd64" \
    GOOS=linux

ENV GOPROXY=https://goproxy.cn
ENV TZ=Asia/Shanghai
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
apk add --no-cache ca-certificates tzdata git
RUN go env -w GOPRIVATE=git.httplogs.com,github.com/klphp
RUN git config --global url."https://klphp:<EMAIL>".insteadOf "https://github.com"

WORKDIR /app
COPY . .
RUN go mod download -d

# 设置容器启动命令为直接运行 go run
CMD ["go", "run", "main.go"]