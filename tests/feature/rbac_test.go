package feature

import (
	"bytes"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/klphp/goravel/tests"
	"github.com/stretchr/testify/suite"
)

type RbacTestSuite struct {
	suite.Suite
	tests.TestCase
	adminToken string
}

func TestRbacTestSuite(t *testing.T) {
	suite.Run(t, new(RbacTestSuite))
}

// SetupTest 在每个测试前运行
func (s *RbacTestSuite) SetupTest() {
	// 设置管理员令牌
	s.adminToken = "pyylr816qqexx|eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXkiOiIxIiwic3ViIjoiYWRtaW4iLCJleHAiOjE3NzkwODY1MTIsImlhdCI6MTc0NzU1MDUxMn0.oOeYeu53zF0LDyJRwTdpG0d0jXvgkVpyFio0SWBCcBk"

	// 打印调试信息
	fmt.Println("RBAC Test running...")
}

// TearDownTest 在每个测试后运行
func (s *RbacTestSuite) TearDownTest() {
}

// TestRoleManagement 测试角色管理功能
func (s *RbacTestSuite) TestRoleManagement() {
	// 1. 创建角色
	requestData := map[string]interface{}{
		"name":       "测试角色",
		"guard_name": "admin",
	}

	jsonBytes, err := json.Marshal(requestData)
	s.Nil(err)

	response, err := s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		WithHeader("Content-Type", "application/json").
		Post("/admin/auth/role", bytes.NewReader(jsonBytes))

	s.Nil(err)
	response.AssertStatus(200)

	// 打印响应内容
	var jsonData map[string]interface{}
	jsonData, err = response.Json()
	s.Nil(err)
	fmt.Printf("Response: %+v\n", jsonData)

	// 检查响应中是否包含status字段
	if status, ok := jsonData["status"]; ok {
		s.Equal(true, status)
	} else {
		s.Fail("Response does not contain status field")
	}

	// 获取角色ID
	roleID := int(jsonData["data"].(map[string]interface{})["id"].(float64))

	// 2. 获取角色列表
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Get("/admin/auth/roles")

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 3. 获取单个角色
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Get(fmt.Sprintf("/admin/auth/role/%d", roleID))

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 4. 更新角色
	updateData := map[string]interface{}{
		"name":       "更新的角色",
		"guard_name": "admin",
	}

	updateBytes, err := json.Marshal(updateData)
	s.Nil(err)

	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		WithHeader("Content-Type", "application/json").
		Put(fmt.Sprintf("/admin/auth/role/%d", roleID), bytes.NewReader(updateBytes))

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 5. 删除角色
	url := fmt.Sprintf("/admin/auth/role/%d", roleID)
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Delete(url, nil)
	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})
}

// TestPermissionManagement 测试权限管理功能
func (s *RbacTestSuite) TestPermissionManagement() {
	// 1. 创建权限
	jsonData := map[string]interface{}{
		"name":       "test.permission",
		"guard_name": "admin",
	}

	jsonBytes, err := json.Marshal(jsonData)
	s.Nil(err)

	response, err := s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		WithHeader("Content-Type", "application/json").
		Post("/admin/auth/permission", bytes.NewReader(jsonBytes))

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 获取权限ID
	respData, err := response.Json()
	s.Nil(err)
	permissionID := int(respData["data"].(map[string]interface{})["id"].(float64))

	// 2. 获取权限列表
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Get("/admin/auth/permissions")

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 3. 获取单个权限
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Get(fmt.Sprintf("/admin/auth/permission/%d", permissionID))

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 4. 更新权限
	updateData := map[string]interface{}{
		"name":       "updated.permission",
		"guard_name": "admin",
	}

	updateBytes, err := json.Marshal(updateData)
	s.Nil(err)

	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		WithHeader("Content-Type", "application/json").
		Put(fmt.Sprintf("/admin/auth/permission/%d", permissionID), bytes.NewReader(updateBytes))

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 5. 删除权限
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Delete(fmt.Sprintf("/admin/auth/permission/%d", permissionID), nil)

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})
}

// TestMenuManagement 测试菜单管理功能
func (s *RbacTestSuite) TestMenuManagement() {
	// 1. 创建菜单
	menuData := map[string]interface{}{
		"name":       "测试菜单",
		"route":      "/test-menu",
		"parent_id":  0,
		"order":      1,
		"icon":       "test-icon",
		"guard_name": "admin",
	}

	jsonBytes, err := json.Marshal(menuData)
	s.Nil(err)

	response, err := s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		WithHeader("Content-Type", "application/json").
		Post("/admin/auth/menu", bytes.NewReader(jsonBytes))

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 获取菜单ID
	menuResp, err := response.Json()
	s.Nil(err)
	menuID := int(menuResp["data"].(map[string]interface{})["id"].(float64))

	// 2. 获取菜单列表
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Get("/admin/auth/menus")

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 3. 获取单个菜单
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Get(fmt.Sprintf("/admin/auth/menu/%d", menuID))

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 4. 更新菜单
	updateData := map[string]interface{}{
		"name":       "更新的菜单",
		"route":      "/updated-menu",
		"parent_id":  0,
		"icon":       "updated-icon",
		"order":      2,
		"guard_name": "admin",
	}

	updateBytes, err := json.Marshal(updateData)
	s.Nil(err)

	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		WithHeader("Content-Type", "application/json").
		Put(fmt.Sprintf("/admin/auth/menu/%d", menuID), bytes.NewReader(updateBytes))

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 5. 获取用户菜单
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Get("/admin/auth/user-menus?model_id=1&model_type=models.Admin")

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 6. 删除菜单
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Delete(fmt.Sprintf("/admin/auth/menu/%d", menuID), nil)

	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})
}

// TestRolePermissionAssignment 测试角色权限分配功能
func (s *RbacTestSuite) TestRolePermissionAssignment() {
	// 1. 创建角色
	roleData := map[string]interface{}{
		"name":       "测试角色2",
		"guard_name": "admin",
	}

	roleBytes, err := json.Marshal(roleData)
	s.Nil(err)

	roleResponse, err := s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		WithHeader("Content-Type", "application/json").
		Post("/admin/auth/role", bytes.NewReader(roleBytes))

	s.Nil(err)
	roleResponse.AssertStatus(200)

	roleResp, err := roleResponse.Json()
	s.Nil(err)
	roleID := int(roleResp["data"].(map[string]interface{})["id"].(float64))

	// 2. 创建权限
	permData := map[string]interface{}{
		"name":       "test.permission2",
		"guard_name": "admin",
	}

	permBytes, err := json.Marshal(permData)
	s.Nil(err)

	permResponse, err := s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		WithHeader("Content-Type", "application/json").
		Post("/admin/auth/permission", bytes.NewReader(permBytes))

	s.Nil(err)
	permResponse.AssertStatus(200)

	permResp, err := permResponse.Json()
	s.Nil(err)
	permissionID := int(permResp["data"].(map[string]interface{})["id"].(float64))

	// 3. 分配权限给角色
	assignData := map[string]interface{}{
		"permission_id": permissionID,
		"role_id":       roleID,
	}

	assignBytes, err := json.Marshal(assignData)
	s.Nil(err)

	assignResponse, err := s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		WithHeader("Content-Type", "application/json").
		Post("/admin/auth/assign-permission-to-role", bytes.NewReader(assignBytes))

	s.Nil(err)
	assignResponse.AssertStatus(200)
	assignResponse.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 4. 从角色中移除权限
	removeData := map[string]interface{}{
		"permission_id": permissionID,
		"role_id":       roleID,
	}

	removeBytes, err := json.Marshal(removeData)
	s.Nil(err)

	removeResponse, err := s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		WithHeader("Content-Type", "application/json").
		Post("/admin/auth/remove-permission-from-role", bytes.NewReader(removeBytes))

	s.Nil(err)
	removeResponse.AssertStatus(200)
	removeResponse.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 5. 删除角色
	url := fmt.Sprintf("/admin/auth/role/%d", roleID)
	response, err := s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Delete(url, nil)
	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})

	// 6. 删除权限
	url = fmt.Sprintf("/admin/auth/permission/%d", permissionID)
	response, err = s.Http(s.T()).
		WithHeader("Authorization", "Bearer "+s.adminToken).
		Delete(url, nil)
	s.Nil(err)
	response.AssertStatus(200)
	response.AssertJson(map[string]interface{}{
		"status": true,
	})
}
