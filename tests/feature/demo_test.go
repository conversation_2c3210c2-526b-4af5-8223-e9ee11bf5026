package feature

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/klphp/goravel/tests"
)

type DemoTestSuite struct {
	suite.Suite
	tests.TestCase
}

func TestDemoTestSuite(t *testing.T) {
	suite.Run(t, new(DemoTestSuite))
}

// SetupTest will run before each test in the suite.
func (s *DemoTestSuite) SetupTest() {
	fmt.Println(`SetupTest 执行...........`)
}

// TearDownTest will run after each test in the suite.
func (s *DemoTestSuite) TearDownTest() {
	fmt.Println(`TearDownTest 执行...........`)
}

func (s *DemoTestSuite) TestIndex() {
	// TODO
	fmt.Println(`TestIndex 执行.............`)
}
