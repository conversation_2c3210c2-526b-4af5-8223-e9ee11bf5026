package feature

import (
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/klphp/goravel/tests"
)

type UserTestSuite struct {
	suite.Suite
	tests.TestCase
}

func TestUserTestSuite(t *testing.T) {
	suite.Run(t, new(UserTestSuite))
}

// SetupTest will run before each test in the suite.
func (s *UserTestSuite) SetupTest() {
}

// TearDownTest will run after each test in the suite.
func (s *UserTestSuite) TearDownTest() {
}

func (s *UserTestSuite) TestIndex() {
	// TODO
	s.True(true)
}
