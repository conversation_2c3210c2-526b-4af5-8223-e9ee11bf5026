# RBAC 权限管理系统测试文档

## 概述

本文档描述了 RBAC 权限管理系统的测试方法和测试用例。RBAC 包提供了角色管理、权限管理、菜单管理以及权限检查等功能，本测试旨在验证这些功能的正确性和可用性。

## 测试环境准备

1. 确保数据库已正确配置并运行
2. 确保已有管理员账户并获取有效的 token

## 测试文件

测试文件位于 `/tests/feature/rbac_test.go`，包含以下测试用例：

1. `TestRoleManagement` - 测试角色的创建、查询、更新和删除
2. `TestPermissionManagement` - 测试权限的创建、查询、更新和删除
3. `TestMenuManagement` - 测试菜单的创建、查询、更新和删除
4. `TestRolePermissionAssignment` - 测试角色权限的分配和移除
5. `TestUserPermissionAssignment` - 测试用户权限的分配和移除
6. `TestUserRoleAssignment` - 测试用户角色的分配和移除

## 运行测试

可以使用以下命令运行所有测试：

```bash
go test -v ./tests/feature/rbac_test.go
```

或者运行特定的测试用例：

```bash
go test -v ./tests/feature/rbac_test.go -run TestRbacTestSuite/TestRoleManagement
```

## 测试 API 端点

测试覆盖了以下 API 端点：

### 角色管理

- `GET /admin/auth/roles` - 获取所有角色
- `GET /admin/auth/role/{id}` - 获取特定角色
- `POST /admin/auth/role` - 创建新角色
- `PUT /admin/auth/role/{id}` - 更新角色
- `POST /admin/auth/remove-role` - 删除角色

### 权限管理

- `GET /admin/auth/permissions` - 获取所有权限
- `GET /admin/auth/permission/{id}` - 获取特定权限
- `POST /admin/auth/permission` - 创建新权限
- `PUT /admin/auth/permission/{id}` - 更新权限
- `POST /admin/auth/remove-permission-from-user` - 删除权限

### 菜单管理

- `GET /admin/auth/menus` - 获取所有菜单
- `GET /admin/auth/menu/{id}` - 获取特定菜单
- `POST /admin/auth/menu` - 创建新菜单
- `PUT /admin/auth/menu/{id}` - 更新菜单
- `GET /admin/auth/user-menus` - 获取用户菜单

### 权限分配

- `POST /admin/auth/assign-permission-to-role` - 分配权限给角色
- `POST /admin/auth/remove-permission-from-role` - 从角色移除权限
- `POST /admin/auth/assign-permission-to-user` - 分配权限给用户
- `POST /admin/auth/remove-permission-from-user` - 从用户移除权限

### 角色分配

- `POST /admin/auth/assign-role` - 分配角色给用户
- `POST /admin/auth/remove-role` - 从用户移除角色
- `GET /admin/auth/user-roles` - 获取用户角色

### 权限检查

- `GET /admin/auth/has-role` - 检查用户是否有特定角色
- `GET /admin/auth/has-permission` - 检查用户是否有特定权限

## 注意事项

1. 测试使用了管理员 token 进行身份验证，确保 token 有效
2. 测试会创建和删除数据，请确保在非生产环境中运行
3. 测试假设管理员用户 ID 为 1，如有不同请调整测试代码
