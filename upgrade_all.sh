#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设置项目根目录
ROOT_DIR=$(pwd)
PACKAGES_DIR="$ROOT_DIR/packages"

# 检查packages目录是否存在
if [ ! -d "$PACKAGES_DIR" ]; then
    echo -e "${RED}错误: packages目录不存在!${NC}"
    exit 1
fi

echo -e "${GREEN}开始升级Goravel扩展包...${NC}"
echo -e "${YELLOW}项目根目录: $ROOT_DIR${NC}"
echo -e "${YELLOW}扩展包目录: $PACKAGES_DIR${NC}"

# 直接使用find命令查找所有包含go.mod的目录
echo -e "${BLUE}正在查找所有Go模块...${NC}"
GO_MODULES=$(find "$PACKAGES_DIR" -name "go.mod" -type f | sort | xargs -n 1 dirname)

# 计数器
TOTAL_PACKAGES=$(echo "$GO_MODULES" | wc -l)
UPGRADED=0
FAILED=0

echo -e "${GREEN}找到 $TOTAL_PACKAGES 个扩展包需要升级${NC}"
echo ""

# 遍历每个Go模块
for DIR in $GO_MODULES; do
    REL_PATH=$(realpath --relative-to="$ROOT_DIR" "$DIR")
    PACKAGE_NAME=$(basename "$DIR")
    
    echo -e "${YELLOW}正在升级扩展包: $PACKAGE_NAME ($REL_PATH)${NC}"
    
    # 进入扩展包目录
    cd "$DIR"
    
    # 执行go get -u
    if go get -u; then
        echo -e "${GREEN}✓ 扩展包 $PACKAGE_NAME 升级成功${NC}"
        UPGRADED=$((UPGRADED + 1))
        
        # 执行go mod tidy
        if go mod tidy; then
            echo -e "${GREEN}✓ 扩展包 $PACKAGE_NAME 依赖整理成功${NC}"
        else
            echo -e "${RED}✗ 扩展包 $PACKAGE_NAME 依赖整理失败${NC}"
        fi
    else
        echo -e "${RED}✗ 扩展包 $PACKAGE_NAME 升级失败${NC}"
        FAILED=$((FAILED + 1))
    fi
    
    # 返回项目根目录
    cd "$ROOT_DIR"
    
    echo ""
done

# 回到项目根目录
cd "$ROOT_DIR"

# 为主项目执行go mod tidy
echo -e "${YELLOW}正在整理主项目依赖...${NC}"
if go mod tidy; then
    echo -e "${GREEN}✓ 主项目依赖整理成功${NC}"
else
    echo -e "${RED}✗ 主项目依赖整理失败${NC}"
fi

# 打印升级结果
echo ""
echo -e "${GREEN}升级完成!${NC}"
echo -e "${GREEN}成功升级: $UPGRADED 个扩展包${NC}"
if [ $FAILED -gt 0 ]; then
    echo -e "${RED}升级失败: $FAILED 个扩展包${NC}"
fi
